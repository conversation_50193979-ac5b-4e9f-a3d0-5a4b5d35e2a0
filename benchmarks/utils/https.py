"""
HTTP请求封装工具
提供统一的HTTP请求接口，包含错误处理、重试机制和日志记录
"""

import json
import time
import uuid
import threading
from typing import Dict, Any, Optional, Union
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from utils.logger import logger


class HTTPError(Exception):
    """HTTP请求异常"""
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class HTTPClient:
    """HTTP客户端封装类"""

    def __init__(self, base_url: str = "", timeout: int = 30, max_retries: int = 3):
        """
        初始化HTTP客户端

        Args:
            base_url: 基础URL
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries

        # 使用线程本地存储来确保每个线程有独立的session
        self._local = threading.local()

        # 设置默认headers
        self.default_headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'CodeRetrievalBenchmarks/1.0.0'
        }

    def _get_session(self):
        """获取当前线程的session，如果不存在则创建"""
        if not hasattr(self._local, 'session'):
            session = requests.Session()

            # 配置重试策略
            retry_strategy = Retry(
                total=self.max_retries,
                status_forcelist=[429, 500, 502, 503, 504],
                allowed_methods=["HEAD", "GET", "OPTIONS", "POST", "PUT", "DELETE"],
                backoff_factor=1
            )

            adapter = HTTPAdapter(max_retries=retry_strategy)
            session.mount("http://", adapter)
            session.mount("https://", adapter)

            # 设置默认headers
            session.headers.update(self.default_headers)

            self._local.session = session

        return self._local.session
    
    def _build_url(self, endpoint: str) -> str:
        """构建完整URL"""
        endpoint = endpoint.lstrip('/')
        if self.base_url:
            return f"{self.base_url}/{endpoint}"
        return endpoint
    
    def _log_request(self, request_id: str, method: str, url: str, **kwargs):
        """记录请求日志"""
        logger.info(f"🚀 [REQUEST START] {request_id} HTTP {method.upper()} {url}")
        if 'json' in kwargs:
            logger.debug(f"[{request_id}] Request body: {json.dumps(kwargs['json'], indent=2)}")

    def _log_response(self, request_id: str, response: requests.Response, start_time: float):
        """记录响应日志"""
        duration = time.time() - start_time
        status_emoji = "✅" if response.status_code < 400 else "❌"
        logger.info(f"{status_emoji} [REQUEST END] {request_id} HTTP {response.status_code} - Duration: {duration:.3f}s")

        if response.status_code >= 400:
            logger.error(f"[{request_id}] Error response: {response.text}")
    
    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """处理响应"""
        try:
            if response.status_code >= 400:
                error_data = None
                try:
                    error_data = response.json()
                except:
                    error_data = {"message": response.text}
                
                raise HTTPError(
                    f"HTTP {response.status_code}: {error_data.get('message', 'Unknown error')}",
                    status_code=response.status_code,
                    response_data=error_data
                )
            
            # 处理空响应
            if not response.content or not response.content.strip():
                logger.warning("Received empty response from server")
                return {"result": {"original_query": "", "code_snippets": [], "iterations": 0}}
            
            # 尝试解析JSON
            try:
                return response.json()
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response:", e)
                logger.error(f"Response content: {response.text[:500]}...")
                raise HTTPError(f"Invalid JSON response: {e}")
                
        except Exception as e:
            logger.error(f"Error handling response:", e)
            raise HTTPError(f"Response handling error: {e}")
    
    def request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        发送HTTP请求

        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 其他请求参数

        Returns:
            响应数据字典

        Raises:
            HTTPError: 请求失败时抛出
        """
        # 生成唯一的请求ID用于日志追踪
        request_id = str(uuid.uuid4())[:8]
        url = self._build_url(endpoint)
        start_time = time.time()

        # 设置超时
        kwargs.setdefault('timeout', self.timeout)

        # 获取当前线程的session
        session = self._get_session()

        self._log_request(request_id, method, url, **kwargs)

        try:
            response = session.request(method, url, **kwargs)
            self._log_response(request_id, response, start_time)
            return self._handle_response(response)

        except requests.exceptions.Timeout:
            logger.error(f"[{request_id}] Request timeout after {self.timeout}s")
            raise HTTPError(f"Request timeout after {self.timeout}s")

        except requests.exceptions.ConnectionError as e:
            logger.error(f"[{request_id}] Connection error: ", e)
            raise HTTPError(f"Connection error: {e}")

        except requests.exceptions.RequestException as e:
            logger.error(f"[{request_id}] Request error: ", e)
            raise HTTPError(f"Request error: {e}")
    
    def get(self, endpoint: str, params: Optional[Dict] = None, **kwargs) -> Dict[str, Any]:
        """GET请求"""
        if params:
            kwargs['params'] = params
        return self.request('GET', endpoint, **kwargs)
    
    def post(self, endpoint: str, data: Optional[Union[Dict, Any]] = None, **kwargs) -> Dict[str, Any]:
        """POST请求"""
        if data is not None:
            kwargs['json'] = data
        return self.request('POST', endpoint, **kwargs)
    
    def put(self, endpoint: str, data: Optional[Union[Dict, Any]] = None, **kwargs) -> Dict[str, Any]:
        """PUT请求"""
        if data is not None:
            kwargs['json'] = data
        return self.request('PUT', endpoint, **kwargs)
    
    def delete(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """DELETE请求"""
        return self.request('DELETE', endpoint, **kwargs)
    
    def set_auth_token(self, token: str):
        """设置认证token"""
        # 更新默认headers，这样新创建的session都会包含这个token
        self.default_headers['Authorization'] = f'Bearer {token}'

        # 如果当前线程已有session，也更新它
        if hasattr(self._local, 'session'):
            self._local.session.headers.update({'Authorization': f'Bearer {token}'})

    def set_header(self, key: str, value: str):
        """设置请求头"""
        # 更新默认headers
        self.default_headers[key] = value

        # 如果当前线程已有session，也更新它
        if hasattr(self._local, 'session'):
            self._local.session.headers.update({key: value})

    def close(self):
        """关闭当前线程的session"""
        if hasattr(self._local, 'session'):
            self._local.session.close()
            delattr(self._local, 'session')
