# Benchmarks

这是一个用于评估代码库检索性能的基准测试工具，主要针对TREO数据集进行评估。

> 由于backend中的项目可能会使用node或者python作为主要语言，因此benchmarks中的语言依赖于backend中的语言依赖无关，运行benchmark需要在当前目录下同步uv的依赖，执行main.py执行评测。

## 项目结构

```
benchmarks/
├── README.md           # 项目说明文档
├── main.py            # 主程序入口
├── pyproject.toml     # 项目依赖配置
├── api/               # API接口模块
│   └── api.py         # CodeBaseDevApi实现
├── common/            # 通用模块
│   └── schemas.py     # 数据模型定义
├── treo/              # TREO基准测试模块
│   ├── treo_benchmark.py  # TREO基准测试实现
│   └── treo_utils.py      # TREO工具函数
├── utils/             # 工具模块
│   ├── https.py       # HTTP客户端
│   └── logger.py      # 日志工具
├── logs/              # 日志文件目录
└── results/           # 测试结果输出目录
```

## 环境要求

- Python >= 3.12
- 依赖包：pydantic, requests, rich, urllib3

## 安装依赖

```bash
# 使用uv安装依赖（推荐）
uv sync

# 或使用pip安装
pip install -r requirements.txt
```

## 配置说明

### main.py 配置变量

在 `main.py` 中，你可以配置以下关键变量：

#### 1. API配置
```python
base_api = CodeBaseDevApi({
    "api_url": "http://localhost:3451/api/v1",  # API服务地址
    "timeout": 300                              # 请求超时时间（秒）
})
```

#### 2. 数据集配置
```python
treo_codebase_benchmark_model = TreoCodebaseBenchmarkModel(
    base_api,
    qas_file="/Users/<USER>/01-Projects/treo/verified/qas_lite.json",  # QA数据集文件路径
    output_dir=output_dir  # 结果输出目录
)
```

#### 3. 评估参数配置
```python
treo_codebase_benchmark_model.evaluate(
    question_key="question",    # 问题字段名，可选值：["question", "question_zh"]
    max_workers=10,            # 并发线程数
    search_tool="any"          # 搜索工具类型
)
```

### 详细配置参数说明

#### API配置参数
- `api_url`: CodeBaseDev API服务的基础URL
- `timeout`: HTTP请求超时时间，单位为秒

#### 数据集配置参数
- `qas_file`: TREO数据集的QA文件路径，JSON格式
- `output_dir`: 评估结果的输出目录

#### 评估参数
- `question_key`: 指定使用哪个问题字段进行评估
  - `"question"`: 英文问题
  - `"question_zh"`: 中文问题（如果数据集包含）
- `max_workers`: 并发处理的线程数，建议根据系统性能调整
- `search_tool`: 搜索工具类型，支持的值：
  - `"term_sparse"`: 基于词项的稀疏检索
  - `"any"`: 任意搜索工具
  - 其他自定义搜索工具名称

#### 输出目录配置
```python
output_dir = "./results/treo/codebase_dev"
time_str = time.strftime("%Y%m%d%H%M%S", time.localtime())
output_dir = os.path.join(output_dir, time_str)
```
结果将保存在带时间戳的子目录中，格式为：`./results/treo/codebase_dev/YYYYMMDDHHMMSS/`

## 运行方法

### 1. 基本运行
```bash
python main.py
```

### 2. 自定义配置运行

你可以修改 `main.py` 中的配置参数，然后运行：

```python
# 示例：修改并发数和搜索工具
def main():
    output_dir = "./results/treo/codebase_dev"
    time_str = time.strftime("%Y%m%d%H%M%S", time.localtime())
    output_dir = os.path.join(output_dir, time_str)

    for question_key in ["question"]:
        base_api = CodeBaseDevApi({
            "api_url": "http://localhost:3451/api/v1",
            "timeout": 600  # 增加超时时间
        })
        treo_codebase_benchmark_model = TreoCodebaseBenchmarkModel(
            base_api,
            qas_file="/path/to/your/qas.json",  # 修改数据集路径
            output_dir=output_dir
        )
        treo_codebase_benchmark_model.init_dataset()
        treo_codebase_benchmark_model.evaluate(
            question_key=question_key,
            max_workers=20,              # 增加并发数
            search_tool="term_sparse"    # 指定搜索工具
        )
```

## 输出结果

### 结果文件
评估完成后，结果将保存在以下位置：
- 路径：`./results/treo/codebase_dev/{timestamp}/`
- 文件名：`treo_joycoder_results_{question_key}_k{k_values}.json`

### 结果格式
```json
{
  "metrics": {
    "precision_at_5": 0.85,
    "recall_at_5": 0.72,
    "f1_at_5": 0.78,
    "map_at_5": 0.68,
    "ndcg_at_5": 0.75,
    "precision_at_10": 0.78,
    "recall_at_10": 0.82,
    "f1_at_10": 0.80,
    "map_at_10": 0.71,
    "ndcg_at_10": 0.77,
    "precision_at_30": 0.65,
    "recall_at_30": 0.92,
    "f1_at_30": 0.76,
    "map_at_30": 0.73,
    "ndcg_at_30": 0.79
  },
  "total_queries": 150,
  "k_values": [5, 10, 30],
  "max_k": 30
}
```

### 日志文件
运行日志保存在 `./logs/` 目录下，文件名格式：`log-YYYY-MM-DD-HH-MM-SS.log`

## 常见问题

### 1. API连接失败
确保CodeBaseDev API服务正在运行，并检查 `api_url` 配置是否正确。

### 2. 数据集文件不存在
检查 `qas_file` 路径是否正确，确保文件存在且格式正确。

### 3. 内存不足
如果处理大型数据集时出现内存问题，可以：
- 减少 `max_workers` 参数值
- 增加系统内存
- 分批处理数据集

### 4. 超时错误
如果出现请求超时，可以：
- 增加 `timeout` 参数值
- 检查网络连接
- 优化API服务性能

## 扩展开发

### 添加新的搜索工具
在 `api/api.py` 中的 `retrieve` 方法中添加对新搜索工具的支持。

### 自定义评估指标
在 `treo/treo_utils.py` 中添加新的评估指标计算方法。

### 支持新的数据集格式
继承 `BenchmarkModel` 类，实现新的数据集处理逻辑。