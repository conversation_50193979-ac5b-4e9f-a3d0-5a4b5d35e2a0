
import math
import hashlib
import os
from typing import Dict, List
from common.schemas import DataSampleTerm

class TreoUtils:
    @staticmethod
    def get_project_id(local_path: str) -> str:
        """从本地路径获取项目ID"""
        project_name = f"treo_{os.path.basename(local_path)}"
        return hashlib.sha256((project_name).encode('utf-8')).hexdigest()

    @staticmethod
    def calculate_metrics(retrieved_docs: List[str], relevant_docs: List[str], top_k: int) -> Dict[str, float]:
        """计算评估指标"""
        if not retrieved_docs or not relevant_docs:
            return {
                f'P@{top_k}': 0.0,
                f'Recall@{top_k}': 0.0,
                f'NDCG@{top_k}': 0.0
            }

        # 取前k个结果
        retrieved_at_k = retrieved_docs[:top_k]
        relevant_set = set(relevant_docs)

        # 计算相关文档数量 - 保持顺序信息
        relevant_retrieved = 0
        for doc in retrieved_at_k:
            if doc in relevant_set:
                relevant_retrieved += 1

        # Precision@K
        precision_at_k = relevant_retrieved / len(retrieved_at_k) if retrieved_at_k else 0.0

        # Recall@K
        recall_at_k = relevant_retrieved / len(relevant_set) if relevant_set else 0.0

        # nDCG@K (normalized Discounted Cumulative Gain)
        # 计算DCG (Discounted Cumulative Gain)
        dcg = 0.0
        for i, doc in enumerate(retrieved_at_k):
            if doc in relevant_set:
                # 每个相关文档的分数默认为1，使用log2(i+2)作为折扣因子
                dcg += 1.0 / math.log2(i + 2)

        # 计算IDCG (Ideal Discounted Cumulative Gain)
        # 理想情况下，所有相关文档都排在前面
        num_relevant = min(len(relevant_set), top_k)
        idcg = 0.0
        for i in range(num_relevant):
            idcg += 1.0 / math.log2(i + 2)

        # 计算nDCG
        ndcg_at_k = dcg / idcg if idcg > 0 else 0.0

        return {
            f'P@{top_k}': precision_at_k,
            f'Recall@{top_k}': recall_at_k,
            f'NDCG@{top_k}': ndcg_at_k
        }

    @staticmethod
    def calculate_metrics_with_map(retrieved_docs: List[str], relevant_docs: List[str], k_values: List[int]) -> Dict[str, float]:
        """计算包含MAP的评估指标"""
        if not retrieved_docs or not relevant_docs:
            metrics = {}
            for k in k_values:
                metrics.update({
                    f'P@{k}': 0.0,
                    f'Recall@{k}': 0.0,
                    f'MAP@{k}': 0.0,
                    f'NDCG@{k}': 0.0,
                })
            return metrics

        relevant_set = set(relevant_docs)
        metrics = {}

        # 计算每个k值的指标
        for k in k_values:
            # 取前k个结果
            retrieved_at_k = retrieved_docs[:k]

            # 计算相关文档数量
            relevant_retrieved = 0
            for doc in retrieved_at_k:
                if doc in relevant_set:
                    relevant_retrieved += 1

            # Precision@K
            precision_at_k = relevant_retrieved / len(retrieved_at_k) if retrieved_at_k else 0.0

            # Recall@K
            recall_at_k = relevant_retrieved / len(relevant_set) if relevant_set else 0.0

            # nDCG@K
            dcg = 0.0
            for i, doc in enumerate(retrieved_at_k):
                if doc in relevant_set:
                    dcg += 1.0 / math.log2(i + 2)

            num_relevant = min(len(relevant_set), k)
            idcg = 0.0
            for i in range(num_relevant):
                idcg += 1.0 / math.log2(i + 2)

            ndcg_at_k = dcg / idcg if idcg > 0 else 0.0

            # MAP@K (Mean Average Precision)
            # 计算Average Precision: 在每个相关文档位置计算precision，然后求平均
            ap_at_k = 0.0
            relevant_count = 0
            for i, doc in enumerate(retrieved_at_k):
                if doc in relevant_set:
                    relevant_count += 1
                    # 计算到当前位置的precision
                    precision_at_i = relevant_count / (i + 1)
                    ap_at_k += precision_at_i

            # Average Precision是所有相关文档位置precision的平均值
            map_at_k = ap_at_k / len(relevant_set) if relevant_set else 0.0

            # 存储指标
            metrics[f'P@{k}'] = precision_at_k
            metrics[f'Recall@{k}'] = recall_at_k
            metrics[f'NDCG@{k}'] = ndcg_at_k
            metrics[f'MAP@{k}'] = map_at_k

        return metrics
    
    @staticmethod
    def convert_to_samples(qas_data: List[Dict], project_ids: Dict[str, str], query_key: str = 'answer_en') -> List[DataSampleTerm]:
        """将TREO数据转换为DataSampleTerm格式"""
        samples = []

        for repo_data in qas_data:
            repo_local_path = repo_data['local_path']
            project_id = project_ids[repo_local_path]
            qa_list = repo_data['qa']

            for qa_item in qa_list:
                question = qa_item[query_key] if query_key in qa_item else qa_item['question']
                answer = qa_item['answer']
                context_paths = qa_item['context']

                # 解析相关文档路径
                context = []
                for context_path in context_paths:
                    file_path = TreoUtils._parse_context_path(context_path)
                    # 移除开头的斜杠，使其与BM25索引中的路径格式一致
                    if file_path.startswith('/'):
                        file_path = file_path[1:]
                    context.append(file_path)

                sample = DataSampleTerm(
                    project_id=project_id,
                    query=question,
                    answer=answer,
                    context=context
                )
                samples.append(sample)

        return samples

    @staticmethod
    def _parse_context_path(context_path: str) -> str:
        """解析context路径，提取文件路径"""
        # context格式: "/path/to/file.py:start_line-end_line"
        if ':' in context_path:
            return context_path.split(':')[0]
        return context_path