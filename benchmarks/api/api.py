import json
import uuid
from typing import Dict
from pydantic import BaseModel

from utils.https import HTT<PERSON>lient
from utils.logger import logger


class SearchResponse(BaseModel):
    query: str
    code_snippets: str

class CodeBaseDevApi:
    def __init__(self, config):
        self.config = config
        self.http_client = HTTPClient(base_url=config['api_url'], timeout=config['timeout'])
        logger.info(f"CodeBaseDevApi initialized with base_url: {config['api_url']}")

    def load_context_files(self):
        pass

    def retrieve(self, query: str, workspaceName: str, search_tool: str = "term_sparse", stream: bool = False, trace_id: str = uuid.uuid4().hex) -> Dict[str, float]:
        try:
            response = self.http_client.post("/searchrouter", data={
                "trace_id": trace_id,
                "query": query,
                "workspace_name": workspaceName,
                "search_tool": search_tool,
                "is_stream": stream
            })
                
            response_data = SearchResponse(**response)

            code_snippets = json.loads(response_data.code_snippets)
            
            return {snippet['file_path']: snippet['score'] for snippet in code_snippets}
            
        except KeyError as e:
            logger.error(f"Missing required field in response:", e)
            return {}
        except Exception as e:
            logger.error(f"Error in retrieve method:", e)
            return {}
    