# 方法 ANY + Side Memory

## 优化字符编码转换和数据类型转换性能，减少CPU开销
TODO: 发现大模型返回的问题与用户的提问问题无关，可能是上下文中干扰过多的问题，可以尝试将读取文件内容简化为只有函数声明的内容[✅]

TODO: 有时大模型返回的读取路径中会包含根目录，在应对子目录读取时，应该有一个基于路径相似度匹配的机制

TODO: 增加对文件依赖的处理，增加对所有语言的检查测试，目前只检查了C/Cpp/Python/Java/TypsCript

TODO: 针对超大工程，应该只允许使用grep的本地操作，禁用其他搜索方式[✅]

TODO: 为searchTool增加数据更新逻辑

TODO: 增加refresh接口用于更新数据

TODO: 增加初始化接口

TODO: 测试ast分块的效果/文件大纲的效果[✅]

TODO: 优化文件目录排序，文件和重要的文件夹应该优先展示

## 实现一个性能基准测试，比较AsyncHttpClient在高并发场景下与传统同步HTTP客户端的性能差异
对于这个问题效果不好，是因为缺少对应的文件上下文，仅仅根据通用语义尝试读取以下文件：
  "client/src/test/java/org/asynchttpclient/channel/MaxConnectionsInThreadsTest.java",
  "client/src/test/java/org/asynchttpclient/channel/MaxTotalConnectionTest.java",
  "client/src/test/java/org/asynchttpclient/testserver/HttpServer.java",
  "client/src/main/java/org/asynchttpclient/DefaultAsyncHttpClient.java",
  "client/src/main/java/org/asynchttpclient/AsyncHttpClient.java",
  "README.md",
  "client/src/test/java/org/apache/commons/fileupload2/MultipartStream.java"
但是Augment的回答是：
 “基于现有的RC1KTest作为参考，需要实现以下测试：1. 创建一个性能基准测试类，测试1000个并发请求的处理能力。2. 对比AsyncHttpClient与HttpURLConnection的吞吐量、延迟和资源使用情况。3. 测试不同连接池配置对性能的影响，包括maxConnections和maxConnectionsPerHost参数。4. 监控内存使用、线程数量和GC压力。5. 测试执行路径：创建测试服务器 -> 配置不同的客户端 -> 执行并发请求 -> 收集性能指标 -> 生成对比报告。需要的上下文包括现有的性能测试框架和连接池配置代码。”
这导致Augment认为相似的上下文基于RC1KTest出发，找到的是直接实现并发基准测试的所需文件。而大模型找到的还是一些和测试相关的文件。

## 实现分布式追踪支持，集成OpenTelemetry进行请求链路追踪

