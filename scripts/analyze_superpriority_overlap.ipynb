{"cells": [{"cell_type": "code", "execution_count": 26, "id": "33e70a41", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing grep: /Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-17-17-33.log\n", "Found 149 unique trace IDs in grep\n", "Found 149 trace-question pairs in grep\n", "Successfully processed 149 traces for grep\n", "Processing term_sparse: /Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-17-39-27.log\n", "Found 149 unique trace IDs in term_sparse\n", "Found 149 trace-question pairs in term_sparse\n", "Successfully processed 149 traces for term_sparse\n", "Processing inverted_index: /Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-17-46-58.log\n", "Found 149 unique trace IDs in inverted_index\n", "Found 149 trace-question pairs in inverted_index\n", "Successfully processed 149 traces for inverted_index\n", "Processing embedding: /Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-17-34-09.log\n", "Found 149 unique trace IDs in embedding\n", "Found 149 trace-question pairs in embedding\n", "Successfully processed 149 traces for embedding\n", "Processing any: /Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-18-16-37.log\n", "Found 149 unique trace IDs in any\n", "Found 149 trace-question pairs in any\n", "Successfully processed 149 traces for any\n"]}], "source": ["import re\n", "import json\n", "from collections import defaultdict\n", "\n", "# 调用1次\n", "# logs_path = {\n", "#     \"grep\": \"/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-01-20-38-21.log\",\n", "#     \"term_sparse\": \"/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-10-34-59.log\",\n", "#     \"inverted_index\": \"/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-10-39-45.log\",\n", "#     \"embedding\": \"/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-15-25-43.log\"\n", "# }\n", "\n", "# 调用2次\n", "logs_path = {\n", "    \"grep\": \"/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-17-17-33.log\",\n", "    \"term_sparse\": \"/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-17-39-27.log\",\n", "    \"inverted_index\": \"/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-17-46-58.log\",\n", "    \"embedding\": \"/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-17-34-09.log\",\n", "    \"any\": \"/Users/<USER>/01-Projects/Codebase-Dev/benchmarks/logs/log-2025-09-02-18-16-37.log\"\n", "}\n", "\n", "# 存储所有工具的结果\n", "all_results = {}  # key为trace_id，value为{\"question\": question, \"recall@5\": recall@5, \"tool\": tool}\n", "question_results = defaultdict(list)  # key为question，value为[{\"tool\": tool, \"recall@5\": recall@5}]\n", "\n", "for tool_name, log_path in logs_path.items():\n", "    print(f\"Processing {tool_name}: {log_path}\")\n", "    \n", "    try:\n", "        with open(log_path, 'r', encoding='utf-8') as f:\n", "            content = f.read()\n", "        \n", "        # 1. 找到所有的trace_id\n", "        trace_pattern = r'Trace: ([a-f0-9]{32})'\n", "        trace_ids = re.findall(trace_pattern, content)\n", "        print(f\"Found {len(set(trace_ids))} unique trace IDs in {tool_name}\")\n", "        \n", "        # 2. 找到所有trace_id对应的question\n", "        question_pattern = r'Trace: ([a-f0-9]{32}), project: [^,]+, question: ([^\\n]+)'\n", "        question_matches = re.findall(question_pattern, content)\n", "        trace_questions = {trace_id: question.strip() for trace_id, question in question_matches}\n", "        print(f\"Found {len(trace_questions)} trace-question pairs in {tool_name}\")\n", "        \n", "        # 3. 找到所有trace_id对应的metrics\n", "        # 匹配metrics块，包括多行JSON\n", "        metrics_pattern = r'Trace: ([a-f0-9]{32}), project: [^,]+, metrics: (\\{[^}]*(?:\\n[^}]*)*\\})'\n", "        metrics_matches = re.findall(metrics_pattern, content, re.MULTILINE | re.DOTALL)\n", "        \n", "        # 4. 解析metrics并提取Recall@5\n", "        for trace_id, metrics_str in metrics_matches:\n", "            try:\n", "                # 清理metrics字符串，移除可能的换行和额外空格\n", "                cleaned_metrics = re.sub(r'\\n\\s*', ' ', metrics_str.strip())\n", "                metrics_json = json.loads(cleaned_metrics)\n", "                \n", "                recall_5 = metrics_json.get('Recall@5', None)\n", "                if recall_5 is not None and trace_id in trace_questions:\n", "                    # 5. 记录trace_id对应的信息\n", "                    all_results[trace_id] = {\n", "                        \"question\": trace_questions[trace_id],\n", "                        \"recall@5\": recall_5,\n", "                        \"tool\": tool_name\n", "                    }\n", "                    \n", "                    # 6. 按question分组\n", "                    question_results[trace_questions[trace_id]].append({\n", "                        \"tool\": tool_name,\n", "                        \"recall@5\": recall_5\n", "                    })\n", "                    \n", "            except json.JSONDecodeError as e:\n", "                print(f\"Failed to parse metrics for trace {trace_id}: {e}\")\n", "                print(f\"Metrics string: {metrics_str[:100]}...\")\n", "                continue\n", "        \n", "        print(f\"Successfully processed {len([t for t in all_results.values() if t['tool'] == tool_name])} traces for {tool_name}\")\n", "        \n", "    except FileNotFoundError:\n", "        print(f\"File not found: {log_path}\")\n", "    except Exception as e:\n", "        print(f\"Error processing {tool_name}: {e}\")"]}, {"cell_type": "code", "execution_count": 27, "id": "03bacfd4", "metadata": {}, "outputs": [{"data": {"text/plain": ["'/Users/<USER>/01-Projects/Codebase-Dev/scripts/sankey.html'"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["from pyecharts.charts import Sankey\n", "from pyecharts import options as opts\n", "\n", "nodes = [\n", "    {\"name\": \"all samples\"},\n", "    {\"name\": \"always resolved\"},\n", "    {\"name\": \"sometimes resolved\"},\n", "    {\"name\": \"never resolved\"},\n", "    {\"name\": \"grep\"},\n", "    {\"name\": \"term_sparse\"},\n", "    {\"name\": \"inverted_index\"},\n", "    {\"name\": \"embedding\"}\n", "]\n", "links = []\n", "\n", "# 统计always resolved, sometimes resolved, never resolved   \n", "always_resolved = []\n", "sometimes_resolved = []\n", "never_resolved = []\n", "resolve_threshold = 0.6\n", "\n", "for question, results in question_results.items():\n", "    resolve_cnt = 0\n", "    for tool_result in results:\n", "        if tool_result[\"recall@5\"] >= resolve_threshold:\n", "            resolve_cnt += 1\n", "    if resolve_cnt == len(results):\n", "        always_resolved.append(question)\n", "    elif resolve_cnt > 0:\n", "        sometimes_resolved.append(question)\n", "    else:\n", "        never_resolved.append(question)\n", "\n", "links.append({\"source\": \"all samples\", \"target\": \"always resolved\", \"value\": len(always_resolved)})\n", "links.append({\"source\": \"all samples\", \"target\": \"sometimes resolved\", \"value\": len(sometimes_resolved)})\n", "links.append({\"source\": \"all samples\", \"target\": \"never resolved\", \"value\": len(never_resolved)})\n", "\n", "# 统计always resolved, sometimes resolved, never resolved for each tool\n", "tool_cnt = defaultdict(int)\n", "for always_question in always_resolved:\n", "    for tool_result in question_results[always_question]:\n", "        tool_cnt[tool_result[\"tool\"]] += 1\n", "for tool, cnt in tool_cnt.items():\n", "    links.append({\"source\": \"always resolved\", \"target\": tool, \"value\": cnt})\n", "\n", "# 统计sometimes resolved for each tool\n", "tool_cnt = defaultdict(int)\n", "for question in sometimes_resolved:\n", "    for tool_result in question_results[question]:\n", "        if tool_result[\"recall@5\"] >= resolve_threshold:\n", "            tool_cnt[tool_result[\"tool\"]] += 1\n", "\n", "for tool, cnt in tool_cnt.items():\n", "    links.append({\"source\": \"sometimes resolved\", \"target\": tool, \"value\": cnt})\n", "# 统计never resolved for each tool\n", "tool_cnt = defaultdict(int)\n", "for question in never_resolved:\n", "    for tool_result in question_results[question]:\n", "        tool_cnt[tool_result[\"tool\"]] += 1\n", "\n", "for tool, cnt in tool_cnt.items():\n", "    links.append({\"source\": \"never resolved\", \"target\": tool, \"value\": cnt})\n", "\n", "# 生成sankey图\n", "pic = (\n", "    Sankey()\n", "    .add('', #图例名称\n", "         nodes,    #传入节点数据\n", "         links,   #传入边和流量数据\n", "         #设置透明度、弯曲度、颜色\n", "         linestyle_opt=opts.LineStyleOpts(opacity = 0.3, curve = 0.5, color = \"source\"),\n", "         #标签显示位置\n", "         label_opts=opts.LabelOpts(position=\"right\"),\n", "         #节点之前的距离\n", "         node_gap = 30,\n", "    )\n", "    .set_global_opts(title_opts=opts.TitleOpts(title = 'Codebase Tool Effectiveness Analysis'))\n", ")\n", "pic.render(\"sankey.html\")\n"]}, {"cell_type": "code", "execution_count": 28, "id": "6cb255a1", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import matplotlib.patches as patches\n", "import numpy as np\n", "\n", "# 设置解决问题的阈值\n", "resolve_threshold = 0.6  # 使用与前面一致的阈值\n", "\n", "# 获取每个工具解决的问题集合\n", "tool_resolved_questions = {tool: set() for tool in logs_path}\n", "for question, results in question_results.items():\n", "    for tool_result in results:\n", "        if tool_result[\"recall@5\"] >= resolve_threshold:\n", "            tool_resolved_questions[tool_result[\"tool\"]].add(question)\n", "\n", "default_colors = [\n", "    # r, g, b, a\n", "    [92, 192, 98, 0.5],\n", "    [90, 155, 212, 0.5],\n", "    [246, 236, 86, 0.6],\n", "    [241, 90, 96, 0.4],\n", "    [255, 117, 0, 0.3],\n", "    [82, 82, 190, 0.2],\n", "]\n", "default_colors = [\n", "    [i[0] / 255.0, i[1] / 255.0, i[2] / 255.0, i[3]]\n", "    for i in default_colors\n", "]\n", "\n", "def draw_text(fig, ax, x, y, text, color=[0, 0, 0, 1], fontsize=14, ha=\"center\", va=\"center\"):\n", "    ax.text(\n", "        x, y, text,\n", "        horizontalalignment=ha,\n", "        verticalalignment=va,\n", "        fontsize=fontsize,\n", "        color=\"black\")\n", "    \n", "def draw_ellipse(fig, ax, x, y, w, h, a, fillcolor):\n", "    e = patches.Ellipse(\n", "        xy=(x, y),\n", "        width=w,\n", "        height=h,\n", "        angle=a,\n", "        color=fillcolor)\n", "    ax.add_patch(e)\n", "\n", "def venn4(labels, names=['A', 'B', 'C', 'D'], **options):\n", "    \"\"\"\n", "    plots a 4-set Venn diagram\n", "\n", "    @type labels: dict[str, str]\n", "    @type names: list[str]\n", "    @rtype: (Figure, AxesSubplot)\n", "\n", "    input\n", "      labels: a label dict where keys are identified via binary codes ('0001', '0010', '0100', ...),\n", "              hence a valid set could look like: {'0001': 'text 1', '0010': 'text 2', '0100': 'text 3', ...}.\n", "              unmentioned codes are considered as ''.\n", "      names:  group names\n", "      more:   colors, figsize, dpi, fontsize\n", "\n", "    return\n", "      pyplot Figure and AxesSubplot object\n", "    \"\"\"\n", "    colors = options.get('colors', [default_colors[i] for i in range(4)])\n", "    figsize = options.get('figsize', (12, 12))\n", "    dpi = options.get('dpi', 96)\n", "    fontsize = options.get('fontsize', 14)\n", "\n", "    fig = plt.figure(0, figsize=figsize, dpi=dpi)\n", "    ax = fig.add_subplot(111, aspect='equal')\n", "    ax.set_axis_off()\n", "    ax.set_ylim(bottom=0.0, top=1.0)\n", "    ax.set_xlim(left=0.0, right=1.0)\n", "\n", "    # body\n", "    draw_ellipse(fig, ax, 0.350, 0.400, 0.72, 0.45, 140.0, colors[0])\n", "    draw_ellipse(fig, ax, 0.450, 0.500, 0.72, 0.45, 140.0, colors[1])\n", "    draw_ellipse(fig, ax, 0.544, 0.500, 0.72, 0.45, 40.0, colors[2])\n", "    draw_ellipse(fig, ax, 0.644, 0.400, 0.72, 0.45, 40.0, colors[3])\n", "    draw_text(fig, ax, 0.85, 0.42, labels.get('0001', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.68, 0.72, labels.get('0010', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.77, 0.59, labels.get('0011', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.32, 0.72, labels.get('0100', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.71, 0.30, labels.get('0101', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.50, 0.66, labels.get('0110', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.65, 0.50, labels.get('0111', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.14, 0.42, labels.get('1000', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.50, 0.17, labels.get('1001', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.29, 0.30, labels.get('1010', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.39, 0.24, labels.get('1011', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.23, 0.59, labels.get('1100', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.61, 0.24, labels.get('1101', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.35, 0.50, labels.get('1110', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.50, 0.38, labels.get('1111', ''), fontsize=fontsize)\n", "\n", "    # legend\n", "    draw_text(fig, ax, 0.13, 0.18, names[0], colors[0], fontsize=fontsize, ha=\"right\")\n", "    draw_text(fig, ax, 0.18, 0.83, names[1], colors[1], fontsize=fontsize, ha=\"right\", va=\"bottom\")\n", "    draw_text(fig, ax, 0.82, 0.83, names[2], colors[2], fontsize=fontsize, ha=\"left\", va=\"bottom\")\n", "    draw_text(fig, ax, 0.87, 0.18, names[3], colors[3], fontsize=fontsize, ha=\"left\", va=\"top\")\n", "    leg = ax.legend(names, loc='center left', bbox_to_anchor=(1.0, 0.5), fancybox=True)\n", "    leg.get_frame().set_alpha(0.5)\n", "\n", "    return fig, ax\n", "\n", "def venn5(labels, names=['A', 'B', 'C', 'D', 'E'], **options):\n", "    \"\"\"\n", "    plots a 5-set Venn diagram\n", "\n", "    @type labels: dict[str, str]\n", "    @type names: list[str]\n", "    @rtype: (Figure, AxesSubplot)\n", "\n", "    input\n", "      labels: a label dict where keys are identified via binary codes ('00001', '00010', '00100', ...),\n", "              hence a valid set could look like: {'00001': 'text 1', '00010': 'text 2', '00100': 'text 3', ...}.\n", "              unmentioned codes are considered as ''.\n", "      names:  group names\n", "      more:   colors, figsize, dpi, fontsize\n", "\n", "    return\n", "      pyplot Figure and AxesSubplot object\n", "    \"\"\"\n", "    colors = options.get('colors', [default_colors[i] for i in range(5)])\n", "    figsize = options.get('figsize', (13, 13))\n", "    dpi = options.get('dpi', 96)\n", "    fontsize = options.get('fontsize', 14)\n", "\n", "    fig = plt.figure(0, figsize=figsize, dpi=dpi)\n", "    ax = fig.add_subplot(111, aspect='equal')\n", "    ax.set_axis_off()\n", "    ax.set_ylim(bottom=0.0, top=1.0)\n", "    ax.set_xlim(left=0.0, right=1.0)\n", "\n", "    # body\n", "    draw_ellipse(fig, ax, 0.428, 0.449, 0.87, 0.50, 155.0, colors[0])\n", "    draw_ellipse(fig, ax, 0.469, 0.543, 0.87, 0.50, 82.0, colors[1])\n", "    draw_ellipse(fig, ax, 0.558, 0.523, 0.87, 0.50, 10.0, colors[2])\n", "    draw_ellipse(fig, ax, 0.578, 0.432, 0.87, 0.50, 118.0, colors[3])\n", "    draw_ellipse(fig, ax, 0.489, 0.383, 0.87, 0.50, 46.0, colors[4])\n", "    draw_text(fig, ax, 0.27, 0.11, labels.get('00001', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.72, 0.11, labels.get('00010', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.55, 0.13, labels.get('00011', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.91, 0.58, labels.get('00100', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.78, 0.64, labels.get('00101', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.84, 0.41, labels.get('00110', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.76, 0.55, labels.get('00111', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.51, 0.90, labels.get('01000', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.39, 0.15, labels.get('01001', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.42, 0.78, labels.get('01010', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.50, 0.15, labels.get('01011', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.67, 0.76, labels.get('01100', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.70, 0.71, labels.get('01101', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.51, 0.74, labels.get('01110', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.64, 0.67, labels.get('01111', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.10, 0.61, labels.get('10000', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.20, 0.31, labels.get('10001', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.76, 0.25, labels.get('10010', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.65, 0.23, labels.get('10011', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.18, 0.50, labels.get('10100', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.21, 0.37, labels.get('10101', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.81, 0.37, labels.get('10110', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.74, 0.40, labels.get('10111', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.27, 0.70, labels.get('11000', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.34, 0.25, labels.get('11001', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.33, 0.72, labels.get('11010', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.51, 0.22, labels.get('11011', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.25, 0.58, labels.get('11100', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.28, 0.39, labels.get('11101', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.36, 0.66, labels.get('11110', ''), fontsize=fontsize)\n", "    draw_text(fig, ax, 0.51, 0.47, labels.get('11111', ''), fontsize=fontsize)\n", "\n", "    # legend\n", "    draw_text(fig, ax, 0.02, 0.72, names[0], colors[0], fontsize=fontsize, ha=\"right\")\n", "    draw_text(fig, ax, 0.72, 0.94, names[1], colors[1], fontsize=fontsize, va=\"bottom\")\n", "    draw_text(fig, ax, 0.97, 0.74, names[2], colors[2], fontsize=fontsize, ha=\"left\")\n", "    draw_text(fig, ax, 0.88, 0.05, names[3], colors[3], fontsize=fontsize, ha=\"left\")\n", "    draw_text(fig, ax, 0.12, 0.05, names[4], colors[4], fontsize=fontsize, ha=\"right\")\n", "    leg = ax.legend(names, loc='center left', bbox_to_anchor=(1.0, 0.5), fancybox=True)\n", "    leg.get_frame().set_alpha(0.5)\n", "\n", "    return fig, ax\n"]}, {"cell_type": "code", "execution_count": 29, "id": "2345c937", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["每个工具解决的问题数量:\n", "grep: 46 个问题\n", "term_sparse: 53 个问题\n", "inverted_index: 37 个问题\n", "embedding: 60 个问题\n", "any: 56 个问题\n"]}, {"data": {"text/plain": ["<Figure size 1200x1000 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1152x960 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import matplotlib\n", "\n", "# 设置中文字体支持\n", "matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']\n", "matplotlib.rcParams['axes.unicode_minus'] = False\n", "\n", "# 设置解决问题的阈值\n", "resolve_threshold = 0.6\n", "\n", "# 获取每个工具解决的问题集合\n", "tool_resolved_questions = {tool: set() for tool in logs_path}\n", "for question, results in question_results.items():\n", "    for tool_result in results:\n", "        if tool_result[\"recall@5\"] >= resolve_threshold:\n", "            tool_resolved_questions[tool_result[\"tool\"]].add(question)\n", "\n", "# 打印每个工具解决的问题数量\n", "print(\"每个工具解决的问题数量:\")\n", "for tool, questions in tool_resolved_questions.items():\n", "    print(f\"{tool}: {len(questions)} 个问题\")\n", "\n", "# 准备五个工具的集合\n", "tools = ['grep', 'term_sparse', 'inverted_index', 'embedding', 'any']\n", "tool_sets = [tool_resolved_questions[tool] for tool in tools]\n", "\n", "# 计算所有可能的交集组合并生成标签字典\n", "labels = {}\n", "\n", "# 生成所有32种可能的组合 (2^5 = 32)\n", "for i in range(32):\n", "    # 将数字转换为5位二进制字符串\n", "    binary = format(i, '05b')\n", "    \n", "    # 根据二进制位确定当前组合包含哪些集合\n", "    current_intersection = None\n", "    for j, bit in enumerate(binary):\n", "        if bit == '1':\n", "            if current_intersection is None:\n", "                current_intersection = tool_sets[j].copy()\n", "            else:\n", "                current_intersection = current_intersection.intersection(tool_sets[j])\n", "    \n", "    # 如果没有选中任何集合，跳过\n", "    if current_intersection is None:\n", "        continue\n", "    \n", "    # 计算这个交集中排除其他集合的元素数量\n", "    exclusive_intersection = current_intersection.copy()\n", "    for j, bit in enumerate(binary):\n", "        if bit == '0':\n", "            exclusive_intersection = exclusive_intersection - tool_sets[j]\n", "    \n", "    # 只有当排除交集不为空时才添加标签\n", "    if len(exclusive_intersection) > 0:\n", "        labels[binary] = str(len(exclusive_intersection))\n", "\n", "# 使用自定义的venn5函数绘制维恩图\n", "plt.figure(figsize=(12, 10))\n", "\n", "# 调用你的venn5函数\n", "fig, ax = venn5(labels, names=tools, figsize=(12, 10), fontsize=12)\n", "\n", "# 设置标题\n", "plt.suptitle(f'Five Tools Problem Solving Overlap (Recall@5 >= {resolve_threshold})', \n", "             fontsize=16, fontweight='bold', y=0.95)\n", "\n", "# 添加统计信息\n", "total_problems = len(set().union(*tool_sets))\n", "stats_text = f\"\"\"Statistics:\n", "Total unique problems: {total_problems}\n", "grep: {len(tool_sets[0])} problems\n", "term_sparse: {len(tool_sets[1])} problems  \n", "inverted_index: {len(tool_sets[2])} problems\n", "embedding: {len(tool_sets[3])} problems\n", "any: {len(tool_sets[4])} problems\"\"\"\n", "\n", "plt.figtext(0.02, 0.02, stats_text, fontsize=10, \n", "           bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 30, "id": "26c7cdea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 工具间重合分析 ===\n", "\n", "两两交集:\n", "grep ∩ term_sparse: 36 个问题 (<PERSON><PERSON><PERSON>相似度: 0.571)\n", "grep ∩ inverted_index: 25 个问题 (<PERSON><PERSON><PERSON>相似度: 0.431)\n", "grep ∩ embedding: 39 个问题 (<PERSON><PERSON><PERSON>相似度: 0.582)\n", "grep ∩ any: 35 个问题 (<PERSON><PERSON><PERSON>相似度: 0.522)\n", "term_sparse ∩ inverted_index: 33 个问题 (<PERSON><PERSON><PERSON>相似度: 0.579)\n", "term_sparse ∩ embedding: 40 个问题 (<PERSON><PERSON><PERSON>相似度: 0.548)\n", "term_sparse ∩ any: 36 个问题 (<PERSON><PERSON><PERSON>相似度: 0.493)\n", "inverted_index ∩ embedding: 30 个问题 (<PERSON><PERSON><PERSON>相似度: 0.448)\n", "inverted_index ∩ any: 30 个问题 (<PERSON><PERSON><PERSON>相似度: 0.476)\n", "embedding ∩ any: 46 个问题 (<PERSON><PERSON><PERSON>相似度: 0.657)\n", "\n", "所有工具共同解决的问题: 20 个\n", "\n", "每个工具独有的问题:\n", "grep 独有: 2 个问题\n", "term_sparse 独有: 7 个问题\n", "inverted_index 独有: 0 个问题\n", "embedding 独有: 5 个问题\n", "any 独有: 5 个问题\n", "\n", "所有工具都能解决的问题示例 (前5个):\n", "1. Analyze PyMySQL's error handling mechanism and check the correctness of exception propagation and er...\n", "2. Please analyze PyMySQL's connection management mechanism in detail, including connection establishme...\n", "3. Please fix input handling issues in the CLI tool, users report abnormal input display in certain ter...\n", "4. Optimize the performance of executemany method for batch inserts, reducing network round trips and m...\n", "5. Please explain in detail the charset and encoding handling mechanism in PyMySQL, including unicode s...\n", "\n", "=== 维恩图标签字典 ===\n", "00001 (any): 5 个问题\n", "00010 (embedding): 5 个问题\n", "00011 (embedding ∩ any): 6 个问题\n", "00101 (inverted_index ∩ any): 2 个问题\n", "00110 (inverted_index ∩ embedding): 1 个问题\n", "00111 (inverted_index ∩ embedding ∩ any): 1 个问题\n", "01000 (term_sparse): 7 个问题\n", "01011 (term_sparse ∩ embedding ∩ any): 2 个问题\n", "01100 (term_sparse ∩ inverted_index): 2 个问题\n", "01110 (term_sparse ∩ inverted_index ∩ embedding): 1 个问题\n", "01111 (term_sparse ∩ inverted_index ∩ embedding ∩ any): 5 个问题\n", "10000 (grep): 2 个问题\n", "10001 (grep ∩ any): 1 个问题\n", "10010 (grep ∩ embedding): 2 个问题\n", "10011 (grep ∩ embedding ∩ any): 5 个问题\n", "11000 (grep ∩ term_sparse): 1 个问题\n", "11010 (grep ∩ term_sparse ∩ embedding): 3 个问题\n", "11011 (grep ∩ term_sparse ∩ embedding ∩ any): 7 个问题\n", "11100 (grep ∩ term_sparse ∩ inverted_index): 1 个问题\n", "11101 (grep ∩ term_sparse ∩ inverted_index ∩ any): 2 个问题\n", "11110 (grep ∩ term_sparse ∩ inverted_index ∩ embedding): 2 个问题\n", "11111 (grep ∩ term_sparse ∩ inverted_index ∩ embedding ∩ any): 20 个问题\n"]}], "source": ["# 打印详细的重合分析\n", "print(\"\\n=== 工具间重合分析 ===\")\n", "\n", "# 计算两两交集\n", "print(\"\\n两两交集:\")\n", "for i in range(len(tools)):\n", "    for j in range(i+1, len(tools)):\n", "        intersection = tool_sets[i].intersection(tool_sets[j])\n", "        union = tool_sets[i].union(tool_sets[j])\n", "        jaccard = len(intersection) / len(union) if len(union) > 0 else 0\n", "        print(f\"{tools[i]} ∩ {tools[j]}: {len(intersection)} 个问题 (J<PERSON>card相似度: {jaccard:.3f})\")\n", "\n", "# 计算所有工具的交集\n", "all_intersection = set.intersection(*tool_sets)\n", "print(f\"\\n所有工具共同解决的问题: {len(all_intersection)} 个\")\n", "\n", "# 计算每个工具独有的问题\n", "print(\"\\n每个工具独有的问题:\")\n", "for i, tool in enumerate(tools):\n", "    unique = tool_sets[i]\n", "    for j, other_set in enumerate(tool_sets):\n", "        if i != j:\n", "            unique = unique - other_set\n", "    print(f\"{tool} 独有: {len(unique)} 个问题\")\n", "\n", "# 如果所有工具都能解决的问题不为空，显示一些例子\n", "if len(all_intersection) > 0:\n", "    print(f\"\\n所有工具都能解决的问题示例 (前5个):\")\n", "    for i, question in enumerate(list(all_intersection)[:5]):\n", "        print(f\"{i+1}. {question[:100]}...\")\n", "\n", "# 打印生成的标签字典以供调试\n", "print(\"\\n=== 维恩图标签字典 ===\")\n", "for binary, count in sorted(labels.items()):\n", "    tool_names = [tools[i] for i, bit in enumerate(binary) if bit == '1']\n", "    print(f\"{binary} ({' ∩ '.join(tool_names)}): {count} 个问题\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "3f64003d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "codebase-dev", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}