import os
import fnmatch
from enum import Enum
from pathlib import Path
from hashlib import sha256
from functools import lru_cache
from pydantic import BaseModel, Field
from typing import List, Optional, Union, Dict, Tuple
from collections import deque

from modules.common.constants import FileFilterMode
from core.config import get_config
from utils.trace_logger import get_trace_logger

# 获取支持 trace 的 logger
logger = get_trace_logger(__name__)

class FileType(Enum):
    CODE = "code"
    DOC = "doc"
    
    @classmethod
    def from_suffix(cls, suffix: str):
        if suffix in [".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".hpp", ".go", ".rs", ".php", ".rb"]:
            return cls.CODE
        else:
            return cls.DOC
        
class FileNode(BaseModel):
    id: str = Field(default_factory=str)
    name: str = Field(default_factory=str)
    type: str = Field(default_factory=str)  # 'file' or 'directory'
    path: str = Field(default_factory=str)
    size: Optional[int] = Field(default=0)
    lastModified: Optional[str] = Field(default="")
    children: Optional[List['FileNode']] = Field(default_factory=list)

    def __str__(self):
        """返回文件树的树形字符串表示"""
        return self._to_tree_string()

    def _to_tree_string(self, prefix: str = "", is_last: bool = True) -> str:
        """
        递归生成树形字符串表示

        Args:
            prefix: 当前行的前缀字符串
            is_last: 是否是同级中的最后一个节点

        Returns:
            树形字符串
        """
        # 当前节点的符号
        current_symbol = "└── " if is_last else "├── "
        
        # 当前节点的完整行
        result = prefix + current_symbol + self.name + "\n"

        # 如果有子节点，递归处理
        if self.children:
            # 为子节点准备前缀
            child_prefix = prefix + ("    " if is_last else "│   ")

            for i, child in enumerate(self.children):
                is_child_last = (i == len(self.children) - 1)
                result += child._to_tree_string(child_prefix, is_child_last)

        return result
    
# 工具函数
def get_file_size(file_path: Path) -> int:
    """获取文件大小"""
    try:
        return file_path.stat().st_size
    except:
        return 0

def get_last_modified(file_path: Path) -> str:
    """获取文件最后修改时间"""
    try:
        return file_path.stat().st_mtime.__str__()
    except:
        return ""

def get_file_filter_config(filter_mode: FileFilterMode) -> Tuple[List[str], List[str], int]:
    """获取文件过滤配置"""
    try:
        config = get_config()
        if filter_mode == FileFilterMode.LOCAL:
            return config.file_filter.local_include, config.file_filter.local_exclude, config.file_filter.max_file_size
        elif filter_mode == FileFilterMode.EMBEDDING:
            return config.file_filter.embedding_include, config.file_filter.embedding_exclude, config.file_filter.max_file_size
        else:
            raise ValueError(f"Unknown filter mode: {filter_mode}")
    except Exception:
        # 配置加载失败时使用默认配置
        logger.error("Failed to load config, using default file filter config")
        
        return (
            [".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".hpp",
                       ".go", ".rs", ".php", ".rb", ".md"],
            ['.git', '.svn', '.hg', 'node_modules', '__pycache__', '.pytest_cache',
            'target', 'build', 'dist', '.next', '.vscode', '.idea', '.DS_Store',
            '*.pyc', '*.pyo', '*.pyd', '.venv'],
            1048576  # 1MB
        )


def should_ignore_path(path: Union[Path | str], filter_mode: FileFilterMode = FileFilterMode.LOCAL) -> bool:
    """根据配置文件中的设置判断是否应该忽略某个路径"""
    include_extensions, exclude_patterns, max_file_size = get_file_filter_config(filter_mode)

    if isinstance(path, str):
        path = Path(path)
    name = path.name

    # 检查排除模式
    for pattern in exclude_patterns:
        if '*' in pattern:
            # 处理通配符模式，如 *.pyc
            if fnmatch.fnmatch(name, pattern):
                return True
        else:
            # 处理精确匹配或目录名
            if name == pattern:
                logger.info(f"Exclude path: {path} by pattern: {pattern}")
                return True

    # 如果是文件，检查扩展名和大小
    if path.is_file():
        # 检查文件扩展名是否在包含列表中
        file_extension = path.suffix.lower()
        if include_extensions and file_extension not in include_extensions:
            return True

        # 检查文件大小
        try:
            if path.stat().st_size > max_file_size or path.stat().st_size == 0:
                return True
        except (OSError, PermissionError):
            # 如果无法获取文件大小，默认不忽略
            pass

    return False

@lru_cache(maxsize=128)
def build_file_tree(root_dir: Union[str | Path], start_dir: Union[str | Path], max_leaf_nodes: int = 5000, filter_mode: FileFilterMode = FileFilterMode.LOCAL) -> List[FileNode]:
    """
    构建文件树，使用宽度优先搜索策略

    Args:
        root_dir: 根目录路径
        max_leaf_nodes: 最大叶子节点数量，用于控制遍历复杂度

    Returns:
        List[FileNode]: 文件树节点列表
    """
    if isinstance(root_dir, str):
        root_dir = Path(root_dir)

    if isinstance(start_dir, str):
        start_dir = Path(start_dir)

    # 存储结果的根节点列表
    root_nodes = []

    # BFS队列：(目录路径, 父节点, 当前深度)
    queue = deque([(start_dir, None, 0)])

    # 记录当前叶子节点数量
    leaf_count = 0

    # 自定义排序函数：目录在前，文件在后，然后按名称字母顺序排序
    def sort_key(item):
        return (0 if item.is_dir() else 1, item.name.lower())

    while queue and leaf_count < max_leaf_nodes:
        current_dir, parent_node, depth = queue.popleft()

        try:
            # 获取当前目录下的所有项目并排序
            items = sorted([item for item in current_dir.iterdir() if not should_ignore_path(item, filter_mode)], key=sort_key)

            # 分离目录和文件
            directories = [item for item in items if item.is_dir()]
            files = [item for item in items if item.is_file()]

            # 处理文件（叶子节点）
            for file_item in files:
                if leaf_count >= max_leaf_nodes:
                    break

                relative_path = os.path.relpath(file_item, root_dir)
                # logger.info(f"dir_item: {file_item}, root_dir: {root_dir} relative_path: {relative_path}")

                file_node = FileNode(
                    id=str(relative_path),
                    name=file_item.name,
                    type="file",
                    path=str(relative_path),
                    size=get_file_size(file_item),
                    lastModified=get_last_modified(file_item)
                )

                if parent_node is None:
                    root_nodes.append(file_node)
                else:
                    parent_node.children.append(file_node)

                leaf_count += 1

            # 处理目录
            for dir_item in directories:
                if leaf_count >= max_leaf_nodes:
                    break

                relative_path = os.path.relpath(dir_item, root_dir)
                # logger.info(f"dir_item: {dir_item}, root_dir: {root_dir} relative_path: {relative_path}")
                dir_node = FileNode(
                    id=str(relative_path),
                    name=dir_item.name,
                    type="directory",
                    path=str(relative_path),
                    children=[]
                )

                if parent_node is None:
                    root_nodes.append(dir_node)
                else:
                    parent_node.children.append(dir_node)

                # 检查是否应该继续遍历这个目录
                should_traverse = _should_traverse_directory(dir_item, leaf_count, max_leaf_nodes)

                if should_traverse:
                    queue.append((dir_item, dir_node, depth + 1))
                else:
                    # 如果不继续遍历，这个目录就是叶子节点
                    leaf_count += 1

        except PermissionError:
            # 权限错误时跳过该目录
            continue

    return root_nodes


def _should_traverse_directory(dir_path: Path, current_leaf_count: int, max_leaf_nodes: int) -> bool:
    """
    判断是否应该继续遍历目录

    Args:
        dir_path: 目录路径
        current_leaf_count: 当前叶子节点数量
        max_leaf_nodes: 最大叶子节点数量

    Returns:
        bool: 是否应该继续遍历
    """
    # 检查叶子节点数量限制
    if current_leaf_count >= max_leaf_nodes:
        return False

    try:
        # 获取目录下的非忽略项目
        items = [item for item in dir_path.iterdir() if not should_ignore_path(item)]

        # 如果目录为空，不需要遍历
        if not items:
            return False

        # 分离目录和文件
        directories = [item for item in items if item.is_dir()]
        files = [item for item in items if item.is_file()]

        # 特殊规则：如果只有一个子目录且没有文件，继续遍历
        if len(directories) == 1 and len(files) == 0:
            return True

        # 如果有文件或多个目录，根据剩余容量决定
        remaining_capacity = max_leaf_nodes - current_leaf_count
        total_items = len(files) + len(directories)

        # 如果剩余容量足够，继续遍历
        return total_items <= remaining_capacity

    except PermissionError:
        return False


def build_file_list(root_dir: Union[str | Path], start_dir: Union[str | Path], filter_mode: FileFilterMode = FileFilterMode.LOCAL) -> Dict[str, str]:
    doc_content = {}
    
    if isinstance(root_dir, str):
        root_dir = Path(root_dir)
    
    if isinstance(start_dir, str):
        start_dir = Path(start_dir)

    file_nodes = build_file_tree(root_dir=root_dir, start_dir=start_dir, max_leaf_nodes=5000, filter_mode=filter_mode)

    def traverse_and_read(node: FileNode):
        if node.type == "file":
            try:
                with open(os.path.join(root_dir, node.path), 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    doc_content[node.path] = "".join(lines)
            except Exception as e:
                logger.error(f"Error reading file {node.path}: {e}")
        else:
            for child in node.children:
                traverse_and_read(child)
    
    for node in file_nodes:
        traverse_and_read(node)
    
    return doc_content

def calculate_repo_size(root_dir: Union[str | Path]):
    """计算仓库大小"""
    if isinstance(root_dir, str):
        root_dir = Path(root_dir)
    
    all_file_nodes = build_file_tree(root_dir=root_dir, start_dir=root_dir, max_leaf_nodes=5000, filter_mode=FileFilterMode.LOCAL)

    # 递归计算总大小
    def calculate_total_size(node: FileNode) -> int:
        if node.type == "file":
            return node.size
        else:
            return sum(calculate_total_size(child) for child in node.children)
    
    return sum(calculate_total_size(node) for node in all_file_nodes)

def generate_file_hash_name(file_path: Union[str | Path], hash_len: str = 32):
    if isinstance(file_path, Path):
        file_path = str(file_path)
    
    assert len(file_path) > 0 and hash_len < 256

    return sha256(file_path.encode()).hexdigest()[:hash_len]

@lru_cache(maxsize=128)
def _read_file_lines_cached(file_full_path: str, file_mtime: float) -> Tuple[str, ...]:
    """
    缓存文件内容的辅助函数

    Args:
        file_full_path: 文件的完整路径
        file_mtime: 文件的修改时间（用于缓存失效）

    Returns:
        Tuple[str, ...]: 文件的所有行（作为元组以便缓存）

    Note:
        使用文件修改时间作为缓存键的一部分，确保文件更新后缓存失效
    """
    try:
        with open(file_full_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        logger.debug(f"Cached file content for: {file_full_path}")
        return tuple(lines)  # 返回元组以便 LRU 缓存
    except Exception as e:
        logger.warning(f"Failed to read file {file_full_path}: {e}")
        return tuple()

def read_chunk_contents(repo_path: str, chunk_paths: List[str]) -> Dict[str, str]:
    """
    读取代码块内容，使用 LRU 缓存减少 IO 次数

    Args:
        repo_path: 仓库路径
        chunk_paths: 代码块路径列表，格式为 "file_path:start_line-end_line"

    Returns:
        Dict[str, str]: 代码块路径到内容的映射

    Note:
        使用 LRU 缓存来缓存文件内容，减少重复读取同一文件的 IO 开销
    """
    file_content = {}

    for chunk_path in chunk_paths:
        try:
            file_path, line_range = chunk_path.split(":")
            start_line, end_line = map(int, line_range.split("-"))

            # 构建完整文件路径
            file_full_path = os.path.join(repo_path, file_path)

            # 获取文件修改时间用于缓存键
            try:
                file_mtime = os.path.getmtime(file_full_path)
            except OSError:
                logger.warning(f"File not found or inaccessible: {file_full_path}")
                file_content[chunk_path] = ""
                continue

            # 使用缓存读取文件内容
            cached_lines = _read_file_lines_cached(file_full_path, file_mtime)

            if not cached_lines:
                file_content[chunk_path] = ""
                continue

            # 提取指定行范围的内容
            # 注意：系统使用0-based行号，与列表索引一致
            lines_list = list(cached_lines)
            selected_lines = lines_list[start_line:end_line + 1]  # end_line是包含的，所以需要+1
            file_content[chunk_path] = "".join(selected_lines).rstrip('\n')

        except ValueError as e:
            logger.warning(f"Invalid chunk_path format '{chunk_path}': {e}")
            file_content[chunk_path] = ""
        except Exception as e:
            logger.warning(f"Error processing chunk_path '{chunk_path}': {e}")
            file_content[chunk_path] = ""

    return file_content