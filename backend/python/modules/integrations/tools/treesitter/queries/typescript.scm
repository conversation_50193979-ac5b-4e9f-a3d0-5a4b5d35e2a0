; TypeScript Tree-sitter 查询模式
; 支持的结构：
; - 函数签名和声明
; - 方法签名和定义
; - 抽象方法签名
; - 类声明（包括抽象类）
; - 模块声明
; - 箭头函数（lambda 函数）
; - switch/case 语句及复杂 case 块
; - 枚举声明及成员
; - 命名空间声明
; - 实用类型
; - 类成员和属性
; - 构造函数方法
; - getter/setter 方法
; - 异步函数和箭头函数
; - Import/Export 语句（依赖分析）

; 函数签名
; 匹配样例: function myFunc(x: number): string;
(function_signature
  name: (identifier) @name.definition.function) @definition.function

; 方法签名
; 匹配样例: interface I { myMethod(x: number): string; }
(method_signature
  name: (property_identifier) @name.definition.method) @definition.method

; 抽象方法签名
; 匹配样例: abstract class A { abstract myMethod(): void; }
(abstract_method_signature
  name: (property_identifier) @name.definition.method) @definition.method

; 抽象类声明
; 匹配样例: abstract class MyAbstractClass { }
(abstract_class_declaration
  name: (type_identifier) @name.definition.class) @definition.class

; 模块声明
; 匹配样例: module MyModule { }
(module
  name: (identifier) @name.definition.module) @definition.module

; 函数声明
; 匹配样例: function myFunction() { }
(function_declaration
  name: (identifier) @name.definition.function) @definition.function

; 方法定义
; 匹配样例: class MyClass { myMethod() { } }
(method_definition
  name: (property_identifier) @name.definition.method) @definition.method

; 类声明
; 匹配样例: class MyClass { }
(class_declaration
  name: (type_identifier) @name.definition.class) @definition.class

; 测试函数调用（describe, test, it）
; 匹配样例: describe("test suite", () => { })
(call_expression
  function: (identifier) @func_name
  arguments: (arguments
    (string) @name
    [(arrow_function) (function_expression)]) @definition.test)
  (#match? @func_name "^(describe|test|it)$")

; 导出测试
; 匹配样例: exports.test = () => { }
(assignment_expression
  left: (member_expression
    object: (identifier) @obj
    property: (property_identifier) @prop)
  right: [(arrow_function) (function_expression)]) @definition.test
  (#eq? @obj "exports")
  (#eq? @prop "test")

; 箭头函数
; 匹配样例: const myFunc = (x: number) => x * 2
(arrow_function) @definition.lambda

; Switch 语句和 case 子句
; 匹配样例: switch (value) { case 1: break; }
(switch_statement) @definition.switch

; 单个 case 子句及其块
; 匹配样例: case 1: console.log("one"); break;
(switch_case) @definition.case

; Default 子句
; 匹配样例: default: console.log("default");
(switch_default) @definition.default

; 枚举声明
; 匹配样例: enum Color { Red, Green, Blue }
(enum_declaration
  name: (identifier) @name.definition.enum) @definition.enum

; 装饰器定义及装饰类
; 匹配样例: @Component export class MyComponent { }
(export_statement
  decorator: (decorator
    (call_expression
      function: (identifier) @name.definition.decorator))
  declaration: (class_declaration
    name: (type_identifier) @name.definition.decorated_class)) @definition.decorated_class

; 显式捕获装饰类中的类名
; 匹配样例: @Component class MyComponent { }
(class_declaration
  name: (type_identifier) @name.definition.class) @definition.class

; 命名空间声明
; 匹配样例: namespace MyNamespace { }
(internal_module
  name: (identifier) @name.definition.namespace) @definition.namespace

; 接口声明（带泛型类型参数和约束）
; 匹配样例: interface MyInterface<T extends string> { }
(interface_declaration
  name: (type_identifier) @name.definition.interface
  type_parameters: (type_parameters)?) @definition.interface

; 类型别名声明（带泛型类型参数和约束）
; 匹配样例: type MyType<T> = T | null
(type_alias_declaration
  name: (type_identifier) @name.definition.type
  type_parameters: (type_parameters)?) @definition.type

; 实用类型
; 匹配样例: type Partial<T> = { [P in keyof T]?: T[P] }
(type_alias_declaration
  name: (type_identifier) @name.definition.utility_type) @definition.utility_type

; 类成员和属性
; 匹配样例: class MyClass { public myProperty: string; }
(public_field_definition
  name: (property_identifier) @name.definition.property) @definition.property

; 构造函数
; 匹配样例: class MyClass { constructor() { } }
(method_definition
  name: (property_identifier) @name.definition.constructor) @definition.constructor
  (#eq? @name.definition.constructor "constructor")

; Getter/Setter 方法
; 匹配样例: class MyClass { get myProp() { } set myProp(value) { } }
(method_definition
  name: (property_identifier) @name.definition.accessor) @definition.accessor

; 异步函数
; 匹配样例: async function myAsyncFunction() { }
(function_declaration
  name: (identifier) @name.definition.async_function) @definition.async_function

; 异步箭头函数
; 匹配样例: const myAsyncArrow = async () => { }
(variable_declaration
  (variable_declarator
    name: (identifier) @name.definition.async_arrow
    value: (arrow_function))) @definition.async_arrow

; const变量声明 - 箭头函数
; 匹配样例: const MyComponent = () => { return <div />; }
(lexical_declaration
  (variable_declarator
    name: (identifier) @name.definition.const_function
    value: (arrow_function))) @definition.const_function

; const变量声明 - 函数表达式
; 匹配样例: const myFunc = function() { return 'hello'; }
(lexical_declaration
  (variable_declarator
    name: (identifier) @name.definition.const_function
    value: (function_expression))) @definition.const_function

; const变量声明 - 对象表达式
; 匹配样例: const config = { api: 'http://localhost', timeout: 5000 }
(lexical_declaration
  (variable_declarator
    name: (identifier) @name.definition.const_object
    value: (object))) @definition.const_object

; const变量声明 - 类表达式
; 匹配样例: const MyClass = class { constructor() {} }
(lexical_declaration
  (variable_declarator
    name: (identifier) @name.definition.const_class
    value: (class))) @definition.const_class

; const变量声明 - 调用表达式（如styled-components）
; 匹配样例: const Container = styled.div`color: red;`
(lexical_declaration
  (variable_declarator
    name: (identifier) @name.definition.const_styled
    value: (call_expression))) @definition.const_styled

; const变量声明 - 模板字面量调用（styled-components）通过call_expression捕获
; 匹配样例: const Button = styled.button`background: blue;`
; 这种情况已经被const_styled规则覆盖了

; const变量声明 - 泛型实例化
; 匹配样例: const api = new ApiClient<User>()
(lexical_declaration
  (variable_declarator
    name: (identifier) @name.definition.const_generic
    value: (new_expression))) @definition.const_generic

; Import 语句
; 匹配样例: import { Component } from 'react';
(import_statement
  source: (string) @name.definition.import) @definition.import

; 命名导入
; 匹配样例: import { useState, useEffect } from 'react';
(import_statement
  (import_clause
    (named_imports
      (import_specifier
        name: (identifier) @name.definition.import_specifier)))) @definition.import

; 默认导入
; 匹配样例: import React from 'react';
(import_statement
  (import_clause
    (identifier) @name.definition.import_default)) @definition.import

; 命名空间导入
; 匹配样例: import * as React from 'react';
(import_statement
  (import_clause
    (namespace_import
      (identifier) @name.definition.import_namespace))) @definition.import

; 类型导入
; 匹配样例: import type { MyType } from './types';
(import_statement
  (import_clause
    (named_imports
      (import_specifier
        name: (identifier) @name.definition.import_type)))) @definition.import

; Export 语句
; 匹配样例: export { Component };
(export_statement
  (export_clause
    (export_specifier
      name: (identifier) @name.definition.export))) @definition.export

; 默认导出
; 匹配样例: export default MyComponent;
(export_statement
  value: (identifier) @name.definition.export_default) @definition.export

; 导出声明
; 匹配样例: export const myVar = 10;
(export_statement
  declaration: (lexical_declaration
    (variable_declarator
      name: (identifier) @name.definition.export_declaration))) @definition.export

; 导出类型
; 匹配样例: export type MyType = string;
(export_statement
  declaration: (type_alias_declaration
    name: (type_identifier) @name.definition.export_type)) @definition.export

; 导出接口
; 匹配样例: export interface MyInterface { }
(export_statement
  declaration: (interface_declaration
    name: (type_identifier) @name.definition.export_interface)) @definition.export

; 重新导出
; 匹配样例: export { Component } from 'react';
(export_statement
  source: (string) @name.definition.reexport) @definition.export

; require 语句（CommonJS）
; 匹配样例: const fs = require('fs');
(variable_declaration
  (variable_declarator
    name: (identifier) @name.definition.require_var
    value: (call_expression
      function: (identifier) @func_name
      arguments: (arguments
        (string) @name.definition.require_source)))) @definition.require
  (#eq? @func_name "require")
