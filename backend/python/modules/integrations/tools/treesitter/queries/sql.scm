; SQL Tree-sitter 查询模式
; 基于实际的 tree-sitter-sql 语法节点类型

; CREATE TABLE 语句
; 匹配样例: CREATE TABLE users (id INTEGER PRIMARY KEY, name VARCHAR(50))
(create_table
  (object_reference
    (identifier) @name.definition.table)) @definition.table

; 列定义
; 匹配样例: id INTEGER PRIMARY KEY, name VARCHAR(50) NOT NULL
(column_definition
  (identifier) @name.definition.column) @definition.column

; SELECT 语句
; 匹配样例: SELECT * FROM users WHERE id = 1
(select) @definition.select

; INSERT 语句
; 匹配样例: INSERT INTO users (name, email) VALUES ('John', '<EMAIL>')
(insert) @definition.insert

; UPDATE 语句
; 匹配样例: UPDATE users SET name = 'Jane' WHERE id = 1
(update) @definition.update

; DELETE 语句
; 匹配样例: DELETE FROM users WHERE id = 1
(delete) @definition.delete

; 函数调用/调用
; 匹配样例: COUNT(*), SUM(salary), MAX(age)
(invocation
  (object_reference
    (identifier) @name.definition.function_call)) @definition.function_call

; 语句块
; 匹配样例: 完整的 SQL 语句
(statement) @definition.statement

; 数据类型
(int) @type.integer
(varchar) @type.varchar
(keyword_boolean) @type.boolean

; 字面量
(literal) @literal.value

; 注释
(comment) @definition.comment
