import os
from utils.file import FileFilterMode, FileNode, build_file_tree
from utils.convert import safe_parse_xml_with_preprocessing, extract_text_from_xml_element


class DirectoryIOTool:
    def __init__(self, repo_path: str):
        self.repo_path = repo_path

    def read(self, query: str) -> str:
        """
        读取目录结构

        Args:
            query: 查询字符串

        Returns:
            str: 目录结构的字符串表示
        """
        # 解析查询参数
        params = self._parse_query(query)

        # 检查目录路径是否存在
        if not params['path']:
            return "Error: No directory path specified"

        try:
            # 调用内部读取方法
            file_tree = self._read_dir_structure(
                relative_dir_path=params['path'],
                max_files=params['max_files']
            )

            # 将FileNode转换为字符串表示
            return f"{file_tree}"

        except FileNotFoundError:
            return f"Error: Directory '{params['path']}' not found"
        except Exception as e:
            return f"Error reading directory: {str(e)}"

    def _parse_query(self, query: str) -> dict:
        """
        解析查询参数，支持XML格式和普通文本

        Args:
            query: 查询字符串

        Returns:
            dict: 解析后的参数字典
        """
        # 默认参数
        params = {
            'path': '',
            'max_files': 100
        }

        # 尝试解析XML格式
        try:
            directory_io_elem = safe_parse_xml_with_preprocessing(query)

            if directory_io_elem is not None:
                # 解析path
                path_elem = directory_io_elem.find('path')
                if path_elem is not None:
                    params['path'] = extract_text_from_xml_element(path_elem).strip()

                # 解析max_files
                max_files_elem = directory_io_elem.find('max_files')
                if max_files_elem is not None:
                    try:
                        params['max_files'] = int(extract_text_from_xml_element(max_files_elem).strip())
                    except ValueError:
                        pass
        except Exception:
            # XML解析失败，尝试作为普通文本处理
            # 假设普通文本就是目录路径
            params['path'] = query.strip()

        return params

    def _read_dir_structure(self, relative_dir_path: str, max_files: int = 100) -> FileNode:
        """
        读取目录内容
        
        Args:
            relative_dir_path: 相对目录路径
            
        Returns:
            str: 目录内容
        """
        absolute_dir_path = os.path.join(self.repo_path, relative_dir_path)
        return FileNode(path=absolute_dir_path, 
                        name=absolute_dir_path.split("/")[-1], 
                        type="directory", 
                        children=build_file_tree(root_dir=self.repo_path, start_dir=absolute_dir_path, max_leaf_nodes=max_files, filter_mode=FileFilterMode.LOCAL))
        
    @property
    def description(self):
        return """- `directory_io`: Directory I/O operations tool for reading directory content.

  Parameters:
  - `path` (required): Relative directory path from repository root
  - `max_files` (optional): Maximum number of files to return (default: 100)

  Use cases:
  - Explore directory structure and contents
  - Identify files of interest for further analysis
"""

    @property
    def examples(self):
        return """<directory_io>
    <path>src</path>
    <max_files>100</max_files>
</directory_io>
"""

