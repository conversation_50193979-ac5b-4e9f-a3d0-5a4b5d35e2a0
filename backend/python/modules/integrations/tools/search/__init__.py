from typing import List
from modules.common.schema import CodeSnippet

def normalize_snippets_scores(snippets: List[CodeSnippet]) -> List[CodeSnippet]:
    """
    Normalize the scores of the snippets to [0, 1]
    
    Args:
        snippets: 代码片段列表
        
    Returns:
        List[CodeSnippet]: 规范化后的代码片段列表
    """
    if not snippets:
        return []

    max_score = max(snippet.score for snippet in snippets)
    min_score = min(snippet.score for snippet in snippets)

    if max_score == min_score:
        return snippets

    for snippet in snippets:
        snippet.score = (snippet.score - min_score) / (max_score - min_score)

    return snippets