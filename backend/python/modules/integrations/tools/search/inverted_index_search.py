import json
from typing import List
import xml.etree.ElementTree as ET
from collections import defaultdict

from modules.integrations.tools.search import normalize_snippets_scores
from modules.integrations.tools.search.abc_search import SearchToolABC
from modules.common.schema import CodeSnippet
from modules.term.term import calculate_inverted_index, calculate_bm25_chunk_terms
from modules.integrations.database.sqlite.client import get_sqlite_client
from modules.integrations.database.schemas import Repo
from utils.trace_logger import get_trace_logger
from utils.convert import safe_parse_xml_with_preprocessing, extract_text_from_xml_element
from utils.file import read_chunk_contents

# 获取支持 trace 的 logger
logger = get_trace_logger(__name__)


class InvertedIndexSearchTool(SearchToolABC):
    def __init__(self, repo_path: str, refresh: bool = False, chunk_keywords_num: int = 20, **kwargs):
        self.repo_path = repo_path

        sqlite_client = get_sqlite_client()
        repo = sqlite_client.get_repo(repo_path)
        
        assert repo is not None, f"Repo {repo_path} not found"

        inverted_index_data = None
        if not repo.inverted_index or refresh:
            # 写入term_sparse数据，为了避免数据更新不一致的情况，这两种数据具有依赖关系的实例的初始化需要加锁
            repo_structures, term_sparse_data = calculate_bm25_chunk_terms(project_dir=repo_path)
            repo.term_sparse = json.dumps(term_sparse_data)
            sqlite_client.update_repo(repo, ["term_sparse"])
            sqlite_client.upsert_repository_key_structure(repo.repo_id, repo_structures)

            inverted_index_data = calculate_inverted_index(project_dir=repo_path, chunk_keywords_num=chunk_keywords_num)
            repo.inverted_index = json.dumps(inverted_index_data)
            sqlite_client.update_repo(repo, ["inverted_index"])
        else:
            inverted_index_data = json.loads(repo.inverted_index)       

        self.keywords2chunk = inverted_index_data['keywords2chunk']
        self.chunk_path2idx = inverted_index_data['chunk_path2idx']
        logger.info(f"Loaded inverted index for {repo_path}")

    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        使用倒排索引搜索代码片段，支持XML格式的查询参数

        Args:
            query: 查询字符串，可以是XML格式或普通文本

        Returns:
            List[CodeSnippet]: 搜索结果
        """
        try:
            # 解析查询参数
            search_params = self._parse_query(query)

            # 获取查询关键词和top_k参数
            query_terms = search_params['query_terms']
            top_k = search_params['top_k']

            # 获取所有关键词对应的chunk
            chunk_scores = defaultdict(int)
            for term in query_terms:
                if term in self.keywords2chunk:
                    for chunk_idx in self.keywords2chunk[term]:
                        chunk_scores[self.chunk_path2idx[chunk_idx]] += 1

            # 按照chunk进行排序
            sorted_chunks = sorted(chunk_scores.items(), key=lambda x: x[1], reverse=True)[:min(top_k, len(chunk_scores))]
            chunk_contents = read_chunk_contents(self.repo_path, [chunk_path for chunk_path, _ in sorted_chunks])

            return normalize_snippets_scores(
                [
                CodeSnippet(
                    file_path=chunk_path.split(":")[0],
                    start_line=int(chunk_path.split(":")[1].split("-")[0]),
                    end_line=int(chunk_path.split(":")[1].split("-")[1]),
                    context_before="",
                    content=chunk_contents[chunk_path],
                    context_after="",
                    score=score
                )
                for chunk_path, score in sorted_chunks
            ]
            )
        except Exception as e:
            logger.error(f"Inverted index search failed: {e}")
            return []

    def _parse_query(self, query: str) -> dict:
        """
        解析查询参数，支持XML格式和普通文本

        Args:
            query: 查询字符串

        Returns:
            dict: 解析后的参数字典
        """
        # 默认参数
        params = {
            'query_terms': query.strip().split(),
            'top_k': 20
        }

        # 尝试解析XML格式
        if query.strip().startswith('<inverted_index>'):
            try:
                # 解析XML
                root = safe_parse_xml_with_preprocessing(query.strip(), 'query')

                # 提取查询关键词
                query_elem = root.find('query')
                if query_elem is not None and query_elem.text:
                    query_content = extract_text_from_xml_element(query_elem)
                    params['query_terms'] = query_content.strip().split()

                # 提取top_k参数
                top_k_elem = root.find('top_k')
                if top_k_elem is not None and top_k_elem.text:
                    try:
                        params['top_k'] = int(top_k_elem.text.strip())
                    except ValueError:
                        logger.warning(f"Invalid top_k value: {top_k_elem.text}, using default 20")

            except ET.ParseError as e:
                logger.warning(f"Failed to parse XML query, using as plain text: {e}")

        return params

    @property
    def description(self):
        return """- `inverted_index`: Keyword-based search engine using inverted index for multi-keyword content discovery. Designed for finding content that relates to any of several related keywords you provide.
**Parameters**:
- query: Multiple related keywords separated by spaces
- top_k: (optional) Maximum results to return (default: 20)

**Performance Tips**:
- Include both high-level concepts AND specific technical terms
- Mix general domain terms with implementation-specific keywords
- Add related system/library function names when applicable
- Include common variable names or patterns from the target domain
"""
    
    @property
    def examples(self):
        return """<output>
    <inverted_index>
    <query>http client request response async session timeout headers authentication</query>
    <top_k>20</top_k>
    </inverted_index>

    <inverted_index>
    <query>database connection SQL query transaction commit rollback cursor execute</query>
    <top_k>15</top_k>
    </inverted_index>

    <inverted_index>
    <query>configuration setup installation deployment environment variables settings</query>
    </inverted_index>
</output>"""
