import logging
import json
from typing import List
from collections import Counter

from core.config import get_config
from modules.common.schema import CodeSnippet
from modules.chunks.chunk_factory import getChunkService
from modules.term.term import get_terms, calculate_bm25_chunk_terms
from modules.integrations.tools.search.abc_search import SearchToolABC
from modules.integrations.database.sqlite.client import get_sqlite_client
from modules.integrations.database.schemas import Repo
from utils.file import FileType, read_chunk_contents
from utils.trace_logger import get_trace_logger
from utils.convert import safe_parse_xml_with_preprocessing, extract_text_from_xml_element
from modules.integrations.tools.search import normalize_snippets_scores

# 获取支持 trace 的 logger
logger = get_trace_logger(__name__)


class TermSparseSearch(SearchToolABC):
    def __init__(self, repo_path: str, refresh: bool = False):
        self.repo_path = repo_path

        self.k1 = 1.2
        self.k2 = 2.0 # Query中词频的重要程度
        self.b = 0.75

        self.term_idf = None
        self.chunks_term_freqs = None
        self.avg_chunk_length = None

        sqlite_client = get_sqlite_client()
        repo = sqlite_client.get_repo(repo_path)

        assert repo is not None, f"Repo {repo_path} not found"
        
        if not repo.term_sparse or refresh:
            key_structures, term_sparse_data = calculate_bm25_chunk_terms(project_dir = repo_path, 
                                                          chunk_splitter=getChunkService(get_config().chunk.name)(),
                                                          k1=self.k1,
                                                          b=self.b
                                                          )
            repo.term_sparse = json.dumps(term_sparse_data)
            sqlite_client.update_repo(repo, ["term_sparse"])
            sqlite_client.upsert_repository_key_structure(repo.repo_id, key_structures)
        else:
            term_sparse_data = json.loads(repo.term_sparse)
        
        self.term_idf = term_sparse_data['term_idf']
        self.chunks_term_freqs = term_sparse_data['chunk_term_freqs']
        self.avg_chunk_length = term_sparse_data['avg_chunk_length']
        logger.info(f"Loaded term sparse for {repo_path}")

    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        使用BM25算法搜索代码片段，支持XML格式的查询参数

        Args:
            query: 查询字符串，可以是XML格式或普通文本

        Returns:
            List[CodeSnippet]: 搜索结果
        """
        if not self.term_idf or not self.chunks_term_freqs:
            return []

        try:
            # 解析查询参数
            search_params = self._parse_query(query)

            # 获取查询参数
            query_text = search_params['query']
            top_k = search_params['top_k']
            file_type = search_params['file_type']

            # 提取查询词
            query_terms = get_terms(query_text, file_type=file_type)

            if not query_terms:
                return []

            # 计算每个文档的匹配分数
            scores = {}
            for chunk_path in self.chunks_term_freqs.keys():
                score = self._calculate_term_sparse_score(query_terms, chunk_path)
                if score > 0:
                    scores[chunk_path] = score

            # 按分数排序并返回top_k结果
            sorted_chunks = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:min(top_k, len(scores))]
            
            chunk_contents = read_chunk_contents(self.repo_path, [chunk_path for chunk_path, _ in sorted_chunks])

            # 返回结果
            return normalize_snippets_scores(
                [CodeSnippet(
                file_path=chunk_path.split(":")[0],
                start_line=int(chunk_path.split(":")[1].split("-")[0]),
                end_line=int(chunk_path.split(":")[1].split("-")[1]),
                context_before="",
                content=chunk_contents[chunk_path],
                context_after="",
                score=scores[chunk_path]
            ) for chunk_path, _ in sorted_chunks]
            )

        except Exception as e:
            logger.error(f"Term sparse search failed: {e}")
            return []

    def _parse_query(self, query: str) -> dict:
        """
        解析查询参数，支持XML格式和普通文本

        支持以下XML格式：
        1. 普通文本: <query>查询内容</query>
        2. CDATA格式: <query><![CDATA[包含<>等特殊字符的查询内容]]></query>
        3. HTML实体转义: <query>包含&lt;等转义字符的查询内容</query>

        Args:
            query: 查询字符串

        Returns:
            dict: 解析后的参数字典
        """
        # 默认参数
        params = {
            'query': query.strip(),
            'top_k': 20,
            'file_type': FileType.CODE
        }

        # 尝试解析XML格式
        if query.strip().startswith('<term_sparse>'):
            # 使用通用的XML预处理和解析函数
            root = safe_parse_xml_with_preprocessing(query.strip(), 'query')

            if root is not None:
                # 提取查询内容
                query_elem = root.find('query')
                if query_elem is not None:
                    query_content = extract_text_from_xml_element(query_elem)
                    if query_content:
                        params['query'] = query_content

                # 提取top_k参数
                top_k_elem = root.find('top_k')
                if top_k_elem is not None and top_k_elem.text:
                    try:
                        params['top_k'] = int(top_k_elem.text.strip())
                    except ValueError:
                        logger.warning(f"Invalid top_k value: {top_k_elem.text}, using default 20")

                # 提取file_type参数
                file_type_elem = root.find('file_type')
                if file_type_elem is not None and file_type_elem.text:
                    file_type_str = file_type_elem.text.strip().lower()
                    if file_type_str == 'code':
                        params['file_type'] = FileType.CODE
                    elif file_type_str == 'doc':
                        params['file_type'] = FileType.DOC
                    else:
                        logger.warning(f"Invalid file_type value: {file_type_elem.text}, using default CODE")
            else:
                logger.warning("Failed to parse XML query, using as plain text")

        return params



    def _calculate_term_sparse_score(self, query_terms: List[str], chunk_path: str) -> float:
        """
        计算单个文档的BM25分数
        """
        if chunk_path not in self.chunks_term_freqs:
            return 0.0
        
        score = 0.0

        for term, term_count in Counter(query_terms).items():
            if term in self.chunks_term_freqs[chunk_path] and term in self.term_idf:
                # logger.info(f"Term: {term}, Chunk: {chunk_path}, TF: {term_freqs[term]}, IDF: {self.term_idf[term]}")
                # 词频
                tf = (term_count * (self.k2 + 1.0)) / (term_count + self.k2)
                
                # IDF值
                idf = self.term_idf[term]

                # BM25公式
                score += idf * idf * tf * self.chunks_term_freqs[chunk_path][term]

        return score

    @property
    def description(self):
        return """- `term_sparse`: BM25-based semantic search engine for fuzzy functionality discovery. Designed for scenarios where you don't have specific keywords or need to find code/documentation by describing general functionality or content themes.
**Parameters**:
- query: Content that mimics the structure and terms of target chunks
- file_type: (optional) "code" (default) for source code, "doc" for documentation
- top_k: (optional) Maximum results to return (default: 20)

**Performance Tips**:
- Query should mimic the actual content structure of target chunks
- For code: use concise pseudo-code that represents the target code structure
- For docs: use representative text snippets that match documentation style
"""

    @property
    def examples(self):
        return """<output>
    <term_sparse>
    <file_type>code</file_type>
    <query>def authenticate_user(username, password): validate_credentials() check_permissions() return auth_token</query>
    <top_k>15</top_k>
    </term_sparse>

    <term_sparse>
    <file_type>code</file_type>
    <query>connection = database.connect(host, port) cursor = connection.cursor() cursor.execute(sql_query)</query>
    <top_k>20</top_k>
    </term_sparse>

    <term_sparse>
    <file_type>doc</file_type>
    <query>Configuration Setup: Edit config.json file. Set database connection parameters: host, port, username, password. Example configuration:</query>
    </term_sparse>
</output>"""

