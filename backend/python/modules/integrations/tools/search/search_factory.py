
import hashlib
import json
import threading
from typing import Dict

from modules.common.constants import SearchToolEnum
from modules.integrations.tools.search.abc_search import SearchToolABC

# 实例池，用于缓存搜索工具实例
INSTANCE_POOL: Dict[str, SearchToolABC] = {}

# 线程锁，确保实例池操作的并发安全
_POOL_LOCK = threading.RLock()

# 全局实例创建锁，确保所有实例创建操作互斥
_CREATION_LOCK = threading.Lock()

def _generate_instance_key(search_tool: SearchToolEnum, repo_path: str, **kwargs) -> str:
    """
    根据入参生成hash值，包括kwargs

    Args:
        search_tool: 搜索工具类型
        repo_path: 仓库路径
        **kwargs: 其他参数

    Returns:
        str: 生成的hash键值
    """
    # 创建包含所有参数的字典
    params = {
        'search_tool': search_tool.value,
        'repo_path': repo_path,
        **kwargs
    }

    # 将参数字典转换为排序后的JSON字符串，确保一致性
    params_str = json.dumps(params, sort_keys=True, ensure_ascii=False)

    # 生成SHA256哈希值
    return hashlib.sha256(params_str.encode('utf-8')).hexdigest()

def get_search_tool_instance(search_tool: SearchToolEnum, repo_path: str, **kwargs) -> SearchToolABC:
    """
    获取搜索工具实例，支持实例池缓存，并发安全
    所有实例创建操作互斥，避免初始化依赖冲突

    Args:
        search_tool: 搜索工具类型
        repo_path: 仓库路径
        **kwargs: 其他参数

    Returns:
        SearchToolABC: 搜索工具实例
    """
    # 根据入参生成hash值，包括kwargs
    instance_key = _generate_instance_key(search_tool, repo_path, **kwargs)

    # 第一次检查：快速路径，如果实例已存在直接返回
    with _POOL_LOCK:
        if instance_key in INSTANCE_POOL:
            return INSTANCE_POOL[instance_key]

    # 使用全局创建锁确保所有实例创建操作互斥
    with _CREATION_LOCK:
        # 在创建锁内再次检查，防止在等待锁的过程中其他线程已经创建了实例
        with _POOL_LOCK:
            if instance_key in INSTANCE_POOL:
                return INSTANCE_POOL[instance_key]

        # 当前线程负责创建实例
        try:
            # 创建新实例（所有创建操作在全局锁中执行，确保互斥）
            instance = _create_search_tool_instance(search_tool, repo_path, **kwargs)

            # 将实例添加到池中
            with _POOL_LOCK:
                INSTANCE_POOL[instance_key] = instance

            return instance

        except Exception as e:
            # 创建失败，重新抛出异常
            raise e


def _create_search_tool_instance(search_tool: SearchToolEnum, repo_path: str, **kwargs) -> SearchToolABC:
    """
    创建搜索工具实例的内部方法

    Args:
        search_tool: 搜索工具类型
        repo_path: 仓库路径
        **kwargs: 其他参数

    Returns:
        SearchToolABC: 搜索工具实例
    """
    if search_tool == SearchToolEnum.GREP:
        from modules.integrations.tools.search.grep_search import GrepSearchTool
        return GrepSearchTool(repo_path)

    elif search_tool == SearchToolEnum.EMBEDDING:
        from modules.integrations.tools.search.embedding_search import EmbeddingSearchTool
        return EmbeddingSearchTool(repo_path, **kwargs)

    elif search_tool == SearchToolEnum.TERM_SPRSE:
        from modules.integrations.tools.search.term_sparse_search import TermSparseSearch
        return TermSparseSearch(repo_path, **kwargs)

    elif search_tool == SearchToolEnum.INVERTED_INDEX:
        from modules.integrations.tools.search.inverted_index_search import InvertedIndexSearchTool
        return InvertedIndexSearchTool(repo_path, **kwargs)
    elif search_tool == SearchToolEnum.COMPELETE:
        from modules.integrations.tools.search.complete_search import CompleteSearchTool
        return CompleteSearchTool(repo_path, **kwargs)
    elif search_tool == SearchToolEnum.ANY:
        from modules.integrations.tools.search.any_search import AnySearchTool
        return AnySearchTool(repo_path, **kwargs)
    else:
        raise ValueError("未知搜索工具")


def clear_instance_pool() -> int:
    """
    清空实例池，释放所有缓存的搜索工具实例

    Returns:
        int: 清理的实例数量
    """
    # 使用全局创建锁确保清理操作与创建操作互斥
    with _CREATION_LOCK:
        with _POOL_LOCK:
            count = len(INSTANCE_POOL)
            INSTANCE_POOL.clear()
            return count


def get_pool_status() -> Dict[str, int]:
    """
    获取实例池状态信息

    Returns:
        Dict[str, int]: 包含实例池大小的字典
    """
    with _POOL_LOCK:
        return {
            'cached_instances': len(INSTANCE_POOL)
        }
