from typing import List, Dict, Union
import xml.etree.ElementTree as ET
from pydantic import BaseModel, Field, ConfigDict
from modules.common.constants import IOToolEnum, SearchToolEnum
from utils.convert import safe_extract_xml_tags_with_preprocessing, extract_text_from_xml_element, safe_parse_xml_with_preprocessing

class Chunk(BaseModel):
    """代码块数据结构"""
    file_path: str = Field(..., description="文件路径", min_length=1)
    start_line: int = Field(..., description="起始行号", ge=0) # 0-based    
    end_line: int = Field(..., description="结束行号", ge=0) # 0-based
    content: str = Field(..., description="代码内容", min_length=0) # 可能为空

class CodeSnippet(Chunk):
    """代码片段数据结构"""
    context_before: str = Field(default="", description="前置上下文")
    context_after: str = Field(default="", description="后置上下文")
    score: float = Field(default=0.0, description="相关性分数")

    def get_full_content(self) -> str:
        """获取完整代码内容，包括上下文"""
        return f"{self.context_before}\n{self.content}\n{self.context_after}"

class SearchResult(BaseModel):
    """搜索结果数据结构"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        frozen=False
    )

    original_query: str = Field(..., description="原始查询字符串", min_length=1)

    all_queries: List[str] = Field(
        default_factory=list,
        description="包含生成的新查询的所有查询列表"
    )
    
    code_snippets: List[CodeSnippet] = Field(
        default_factory=list,
        description="搜索到的代码片段列表"
    )

    iterations: int = Field(
        default=0,
        description="搜索迭代次数",
        ge=0
    )

    def get_summary(self) -> str:
        """获取搜索结果摘要"""
        return f"""搜索摘要:
- 原始查询: {self.original_query}
- 总查询数量: {len(self.all_queries)}
- 找到的代码片段: {len(self.code_snippets)}
- 迭代次数: {self.iterations}
"""

class ContextOperation(BaseModel):
    tool: Union[IOToolEnum, SearchToolEnum] = Field(..., description="上下文操作工具")
    xml_content: str = Field(..., description="上下文操作XML内容")
    context_uri: str = Field(..., description="上下文操作URI")

    def __str__(self):
        return f"ContextOperation(tool={self.tool}, context_uri={self.context_uri}, xml_content={self.xml_content})"

class ContextOperationResult(BaseModel):
    operation: ContextOperation = Field(..., description="上下文操作")
    result: str = Field(..., description="操作结果")

    def __str__(self):
        return f"- {self.operation.tool.value}: {self.operation.context_uri}:\n```\n{'\n'.join(self.result.splitlines()[:100])}\n```\n"

class SearchQuery(BaseModel):
    text: str = Field(..., description="子查询文本")
    context_operations: List[ContextOperation] = Field(..., description="上下文操作列表")

    @staticmethod
    def parse_response_text(response_text: str) -> List['SearchQuery']:
        """
        解析响应文本，提取子查询信息

        Args:
            response_text: 包含XML格式的响应文本

        Returns:
            List[SearchQuery]: 解析出的子查询列表

        Examples:
            >>> response = '''<output>
            ...     <sub_query>
            ...         <context>
            ...             <file_io><path>test.py</path></file_io>
            ...         </context>
            ...         <text>Search for test functions</text>
            ...     </sub_query>
            ... </output>'''
            >>> queries = SearchQuery.parse_response_text(response)
            >>> print(len(queries))
        """
        search_queries = []

        # 使用安全的XML标签提取函数
        extracted_tags = safe_extract_xml_tags_with_preprocessing(response_text, ['sub_query'])
        
        for _, tag_content in extracted_tags:
            try:
                # 解析单个sub_query标签
                root = safe_parse_xml_with_preprocessing(tag_content)
                if root is None:
                    continue

                # 提取text内容
                text_elem = root.find('text')
                if text_elem is None:
                    continue

                text_content = extract_text_from_xml_element(text_elem)
                if not text_content:
                    continue

                # 提取context操作
                context_operations: List[ContextOperation] = []
                context_elem = root.find('context')
                if context_elem is not None:
                    # 查找file_io操作
                    for io_tool in IOToolEnum:
                        for io_elem in context_elem.findall(io_tool.value):
                            path_elem = io_elem.find('path')
                            if path_elem is not None:
                                path_content = extract_text_from_xml_element(path_elem)
                                if path_content:
                                    # 将XML元素转换为字符串
                                    io_xml = ET.tostring(io_elem, encoding='unicode')
                                    context_operations.append(ContextOperation(
                                        tool=io_tool,
                                        xml_content=io_xml,
                                        context_uri=path_content
                                    ))
                # 创建SubQuery对象
                sub_query = SearchQuery(
                    text=text_content,
                    context_operations=context_operations
                )
                search_queries.append(sub_query)

            except Exception:
                # 如果解析单个sub_query失败，跳过并继续处理下一个
                continue
        
        # 解析Search操作
        search_tool_tags = [tool.value for tool in SearchToolEnum]
        structure_tags = safe_extract_xml_tags_with_preprocessing(response_text, search_tool_tags)

        for tag_name, tag_content in structure_tags:
            try:
                # 根据标签名找到对应的工具枚举
                tool_enum = None
                for tool in SearchToolEnum:
                    if tool.value == tag_name:
                        tool_enum = tool
                        break

                if tool_enum is None:
                    continue

                # 为每个搜索工具查询创建一个SearchQuery对象
                # context_uri设为空，因为这种格式没有特定的上下文操作
                context_operation = ContextOperation(
                    tool=tool_enum,
                    xml_content=tag_content,
                    context_uri=""
                )

                # 从XML内容中提取文本作为查询文本
                # 对于搜索工具标签，尝试提取query子标签的内容
                text_content = tag_content
                try:
                    root = safe_parse_xml_with_preprocessing(tag_content)
                    if root is not None:
                        # 首先尝试提取query子标签
                        query_elem = root.find('query')
                        if query_elem is not None:
                            extracted_text = extract_text_from_xml_element(query_elem)
                            if extracted_text:
                                text_content = extracted_text
                        else:
                            # 如果没有query子标签，提取所有文本内容
                            extracted_text = extract_text_from_xml_element(root)
                            if extracted_text:
                                text_content = extracted_text
                except Exception:
                    # 如果XML解析失败，使用原始内容
                    pass

                search_query = SearchQuery(
                    text=text_content,
                    context_operations=[context_operation]
                )
                search_queries.append(search_query)

            except Exception:
                # 如果创建SearchQuery失败，跳过并继续处理下一个
                continue

        return search_queries