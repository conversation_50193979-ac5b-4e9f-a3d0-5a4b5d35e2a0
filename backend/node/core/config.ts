import yaml from 'js-yaml';
import fs from 'fs';
import path from 'path';

export interface LogConfig {
    dir: string;
    name: string;
    level: string;
    max_size_m: number;
}

export interface CorsConfig {
    origins: string[];
    allow_credentials: boolean;
    allow_methods: string[];
    allow_headers: string[];
}

export interface ApiConfig {
    host: string;
    port: number;
    reload: boolean;
    cors: CorsConfig;
    version: number;
}

export interface DataConfig {
    repos_path: string;
    ignored_terms_path: string;
}

export interface LLMConfig {
    base_url: string;
    api_key: string;
    model: string;
    temperature: number;
    max_tokens: number;
    timeout: number;
}

export interface ParallelFilterConfig {
    enabled: boolean;
    min_snippets_for_parallel: number;
    max_workers: number;
    progress_interval: number;
}

export interface DeepSearchConfig {
    max_iterations: number;
    max_sub_queries: number;
    max_new_queries: number;
    parallel_filter: ParallelFilterConfig;
    enabled_search_tools: string[];
}

export interface FileFilterConfig {
    local_exclude: string[];
    local_include: string[];
    embedding_exclude: string[];
    embedding_include: string[];
    max_file_size: number;
}

export interface ChunkConfig {
    name: string;
    min_chunk_size: number;
    max_chunk_size: number;
    overflow_size: number;
}

export interface SQLiteConfig {
    db_path: string;
    timeout: number;
    check_same_thread: boolean;
    isolation_level?: string;
}

export interface DatabaseConfig {
    sqlite: SQLiteConfig;
}

export interface Config {
    log: LogConfig;
    api: ApiConfig;
    data: DataConfig;
    llm: LLMConfig;
    deepsearch: DeepSearchConfig;
    file_filter: FileFilterConfig;
    chunk: ChunkConfig;
    database: DatabaseConfig;
}

let _config: { [env: string]: Config } = {};

export function loadConfig(env: string): Config {
    if (_config[env]) {
        return _config[env];
    }

    const configDir = path.join(__dirname, '../config');
    let configPath = path.join(configDir, `config.${env}.yaml`);

    if (!fs.existsSync(configPath)) {
        configPath = path.join(configDir, 'config.yaml');
    }

    if (!fs.existsSync(configPath)) {
        throw new Error(`Config file not found: ${configPath}`);
    }

    try {
        const configData = yaml.load(fs.readFileSync(configPath, 'utf8')) as Config;
        _config[env] = configData;
        return _config[env];
    } catch (e) {
        if (e instanceof Error) {
            throw new Error(`Error parsing YAML file: ${e.message}`);
        }
        throw new Error('An unknown error occurred while parsing the YAML file.');
    }
}

export function getConfig(env: string = process.env.ENV || 'dev'): Config {
    return loadConfig(env);
}

export function setConfig(config: Config, env: string = process.env.ENV || 'dev'): void {
    _config[env] = config;
}