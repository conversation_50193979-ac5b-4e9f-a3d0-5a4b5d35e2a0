import { Router, Request, Response } from 'express';
import path from 'path';
import fs from 'fs';
import { getConfig } from '../../../core/config';
import { shouldIgnorePath, buildFileTree } from '../../../utils/file';
import { FileFilterMode, SearchToolEnum } from '../../../modules/common/constants';
import { WorkspaceInfo, SearchRequest, SearchResponse } from './schema';
import { DeepSearch } from '../../../modules/deepsearch/deep_search';
import { SearchRouter } from '../../../modules/searchrouter/search_router';
import { getLogger } from '../../../utils/trace_logger';

const logger = getLogger(__filename);

const router = Router();

router.get('/workspaces', (req: Request, res: Response) => {
    const reposPath = path.resolve(getConfig().data.repos_path);
    if (!fs.existsSync(reposPath)) {
        return res.status(404).send('Repos directory not found');
    }

    try {
        const workspaces: WorkspaceInfo[] = fs.readdirSync(reposPath, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory() && !shouldIgnorePath(path.join(reposPath, dirent.name)))
            .map(dirent => ({
                id: dirent.name,
                name: dirent.name,
                path: path.join(reposPath, dirent.name),
                description: `${dirent.name} repository`
            }));
        res.json(workspaces);
    } catch (error) {
        if (error instanceof Error) {
            res.status(500).send(`Failed to read workspaces: ${error.message}`);
        }
    }
});

router.get('/workspaces/:workspaceName/files', (req: Request, res: Response) => {
    const { workspaceName } = req.params;
    const workspacePath = path.resolve(path.join(getConfig().data.repos_path, workspaceName));

    if (!fs.existsSync(workspacePath)) {
        return res.status(404).send('Workspace not found');
    }

    if (!fs.statSync(workspacePath).isDirectory()) {
        return res.status(400).send('Workspace is not a directory');
    }

    try {
        const fileTree = buildFileTree(workspacePath, workspacePath, 1000, FileFilterMode.LOCAL);
        res.json(fileTree);
    } catch (error) {
        if (error instanceof Error) {
            res.status(500).send(`Failed to read file tree: ${error.message}`);
        }
    }
});

router.get('/files', (req: Request, res: Response) => {
    const filePath = req.query.file_path as string;
    if (!filePath) {
        return res.status(400).send('File path is required');
    }

    try {
        const reposPath = path.resolve(getConfig().data.repos_path);
        const fullPath = path.resolve(reposPath, filePath);

        if (!fullPath.startsWith(reposPath)) {
            return res.status(403).send('Access denied');
        }

        if (!fs.existsSync(fullPath)) {
            return res.status(404).send('File not found');
        }

        if (!fs.statSync(fullPath).isFile()) {
            return res.status(400).send('Path is not a file');
        }

        try {
            const content = fs.readFileSync(fullPath, 'utf-8');
            res.json({ content });
        } catch (error) {
            res.json({ content: `Binary file: ${path.basename(fullPath)}\nFile size: ${fs.statSync(fullPath).size} bytes` });
        }
    } catch (error) {
        if (error instanceof Error) {
            res.status(500).send(`Failed to read file: ${error.message}`);
        }
    }
});


// POST /searchrouter - 使用SearchRouter进行代码搜索
router.post('/searchrouter', async (req: Request, res: Response) => {
    try {
        const request: SearchRequest = req.body;
        
        // 验证必需字段
        if (!request.query || !request.workspace_path) {
            return res.status(400).json({ error: 'query and workspace_name are required' });
        }

        logger.info(`开始处理SearchRouter搜索请求: query='${request.query}', workspace='${request.workspace_path}'`);

        if (!fs.existsSync(request.workspace_path)) {
            logger.error(`工作空间不存在: ${request.workspace_path}`);
            return res.status(404).json({ error: 'Workspace not found' });
        }
        const searchTool = request.search_tool ? SearchToolEnum[request.search_tool.toUpperCase() as keyof typeof SearchToolEnum] : SearchToolEnum.TERM_SPRSE;
        const searchRouter = new SearchRouter(request.workspace_path, request.workspace_name, null, searchTool);
        await searchRouter.init();

        if (!request.is_stream) {
            logger.info('执行异步非流式搜索');
            const result = await searchRouter.search(request.query);
            logger.info(`异步搜索完成，找到 ${result.codeSnippets.length} 个代码片段`);
            
            const maxResults = request.max_results || 20;
            const limitedSnippets = result.codeSnippets.slice(0, Math.min(maxResults, result.codeSnippets.length));
            
            const response: SearchResponse = {
                query: result.originalQuery,
                code_snippets: JSON.stringify(limitedSnippets.map(snippet => ({
                    file_path: snippet.filePath,
                    content: snippet.content,
                    start_line: snippet.startLine,
                    end_line: snippet.endLine,
                    score: snippet.score || 0
                })), null, 2)
            };
            
            return res.json(response);
        }

        // 流式响应
        logger.info('执行流式搜索');
        res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': '*',
            'X-Accel-Buffering': 'no',
            'Transfer-Encoding': 'chunked'
        });

        // 使用SearchRouter的流式搜索
        try {
            for await (const chunk of searchRouter.searchStream(request.query)) {
                res.write(chunk);
            }
            res.end();
        } catch (streamError) {
            logger.error(`SearchRouter流式搜索失败: ${streamError}`);
            res.write(`data: ${JSON.stringify({"type": "error", "message": `搜索失败: ${streamError}`, "timestamp": Date.now()})}\n\n`);
            res.end();
        }
        
    } catch (error) {
        logger.error(`SearchRouter搜索请求处理失败: ${error}`);
        if (!res.headersSent) {
            res.status(500).json({ error: `SearchRouter failed: ${error instanceof Error ? error.message : 'Unknown error'}` });
        }
    }
});

export default router;