export interface WorkspaceInfo {
    id: string;
    name: string;
    path: string;
    description?: string;
}

export interface SearchRequest {
    trace_id: string;
    query: string;
    workspace_path: string;
    search_tool?: 'grep' | 'term_sparse' | 'keywords';
    is_stream?: boolean;
    max_results?: number;
}

export interface SearchResponse {
    query: string;
    code_snippets: string;
}