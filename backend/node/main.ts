import express, { Request, Response } from 'express';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import { getConfig } from './core/config';
import apiV1 from './server/api/v1/api';

async function main() {
    const argv = await yargs(hideBin(process.argv)).options({
      env: { type: 'string', default: 'dev', description: 'Environment to run' },
    }).argv;

    process.env.ENV = argv.env;

    const config = getConfig(); 

    const app = express();
    const port = config.api.port || 3000;

    // 添加JSON解析中间件
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));

    app.use('/api/v1', apiV1);

    app.get('/', (req: Request, res: Response) => {
      res.send('Hello World!');
    });

    app.listen(port, () => {
      console.log(`Server is running at http://localhost:${port} with hot reload enabled`);
    });
}

main();