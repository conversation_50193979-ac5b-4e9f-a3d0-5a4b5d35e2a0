# Node.js 后端开发指南

## 启动方式

### 生产模式
```bash
npm start
```

### 开发模式（热更新）
```bash
npm run dev
```

## 热更新功能

开发模式使用 `nodemon` 实现热更新，当以下文件发生变化时会自动重启服务器：

- `main.ts` - 主入口文件
- `server/**/*` - 服务器相关文件
- `modules/**/*` - 模块文件
- `core/**/*` - 核心文件
- `utils/**/*` - 工具文件
- `config/**/*` - 配置文件

## 配置文件

- `nodemon.json` - nodemon 配置文件
- `package.json` - 项目依赖和脚本配置

## 手动重启

在开发模式下，可以在终端中输入 `rs` 来手动重启服务器。

## 注意事项

- 数据库文件 `db.sqlite` 和 `data/**/*` 目录的变化不会触发重启
- 测试文件 `*.test.ts` 和 `*.spec.ts` 的变化不会触发重启
- `node_modules` 目录被忽略