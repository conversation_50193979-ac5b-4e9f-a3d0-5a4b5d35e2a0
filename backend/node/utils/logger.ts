import winston from 'winston';
import 'winston-daily-rotate-file';
import path from 'path';
import fs from 'fs';
import { getConfig } from '../core/config';

const logConfig = getConfig().log;

const logDir = path.join(__dirname, '../../', logConfig.dir);

if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

const dailyRotateFileTransport = new winston.transports.DailyRotateFile({
  filename: `${logDir}/${logConfig.name}-%DATE%.log`,
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: `${logConfig.max_size_m}m`,
  maxFiles: '14d',
});

const logger = winston.createLogger({
  level: logConfig.level,
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.printf(info => `${info.timestamp} ${info.level}: ${info.message}`)
  ),
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.printf(info => `${info.timestamp} ${info.level}: ${info.message}`)
      )
    }),
    dailyRotateFileTransport
  ]
});

logger.info(`Log system initialized - Level: ${logConfig.level}, File: ${logDir}/${logConfig.name}-%DATE%.log, Max size: ${logConfig.max_size_m}MB`);

export default logger;