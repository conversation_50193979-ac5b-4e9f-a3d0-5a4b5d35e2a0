import * as fs from 'fs';
import * as path from 'path';
import { getConfig } from '../core/config';
import { FileFilterMode } from '../modules/common/constants';
const minimatch = require('minimatch');
import { Dirent } from 'fs';

export enum FileType {
    CODE = 'code',
    DOC = 'doc'
}

export interface FileNode {
    id: string;
    name: string;
    type: 'file' | 'directory';
    path: string;
    size?: number;
    lastModified?: string;
    children?: FileNode[];
}

function getFileFilterConfig(filterMode: FileFilterMode): [string[], string[], number] {
    try {
        const config = getConfig();
        if (filterMode === FileFilterMode.LOCAL) {
            return [config.file_filter.local_include, config.file_filter.local_exclude, config.file_filter.max_file_size];
        } else if (filterMode === FileFilterMode.EMBEDDING) {
            return [config.file_filter.embedding_include, config.file_filter.embedding_exclude, config.file_filter.max_file_size];
        } else {
            throw new Error(`Unknown filter mode: ${filterMode}`);
        }
    } catch (error) {
        console.error("Failed to load config, using default file filter config");
        return [
            [".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".hpp", ".go", ".rs", ".php", ".rb", ".md"],
            ['.git', '.svn', '.hg', 'node_modules', '__pycache__', '.pytest_cache', 'target', 'build', 'dist', '.next', '.vscode', '.idea', '.DS_Store', '*.pyc', '*.pyo', '*.pyd', '.venv'],
            1048576 // 1MB
        ];
    }
}

export function shouldIgnorePath(filePath: string, filterMode: FileFilterMode = FileFilterMode.LOCAL): boolean {
    const [includeExtensions, excludePatterns, maxFileSize] = getFileFilterConfig(filterMode);
    const name = path.basename(filePath);

    // 检查排除模式
    for (const pattern of excludePatterns) {
        if (pattern.includes('*')) {
            // 处理通配符模式，如 *.pyc
            if (minimatch(name, pattern)) {
                return true;
            }
        } else {
            // 处理精确匹配或目录名
            if (name === pattern) {
                return true;
            }
        }
    }

    // 如果是文件，检查扩展名和大小
    try {
        const stats = fs.statSync(filePath);
        if (stats.isFile()) {
            // 检查文件扩展名是否在包含列表中
            const fileExtension = path.extname(filePath).toLowerCase();
            if (includeExtensions.length > 0 && !includeExtensions.includes(fileExtension)) {
                return true;
            }

            // 检查文件大小
            if (stats.size > maxFileSize) {
                return true;
            }
        }
    } catch (error) {
        // 如果无法获取文件状态，默认不忽略
    }

    return false;
}

export function calculateRepoSize(rootDir: string): number {
    const fileNodes = buildFileTree(rootDir, rootDir, 5000, FileFilterMode.LOCAL);
    
    // 递归计算总大小
    function calculateTotalSize(node: FileNode): number {
        if (node.type === 'file') {
            return node.size || 0;
        } else {
            return (node.children || []).reduce((total, child) => total + calculateTotalSize(child), 0);
        }
    }
    
    return fileNodes.reduce((total, node) => total + calculateTotalSize(node), 0);
}

/**
 * 判断是否应该继续遍历目录
 * 
 * @param dirPath 目录路径
 * @param currentLeafCount 当前叶子节点数量
 * @param maxLeafNodes 最大叶子节点数量
 * @param filterMode 过滤模式
 * @returns 是否应该继续遍历
 */
function shouldTraverseDirectory(dirPath: string, currentLeafCount: number, maxLeafNodes: number, filterMode: FileFilterMode): boolean {
    // 检查叶子节点数量限制
    if (currentLeafCount >= maxLeafNodes) {
        return false;
    }

    try {
        // 获取目录下的非忽略项目
        const items = fs.readdirSync(dirPath, { withFileTypes: true })
            .filter(item => !shouldIgnorePath(path.join(dirPath, item.name), filterMode));

        // 如果目录为空，不需要遍历
        if (items.length === 0) {
            return false;
        }

        // 分离目录和文件
        const directories = items.filter(item => item.isDirectory());
        const files = items.filter(item => item.isFile());

        // 特殊规则：如果只有一个子目录且没有文件，继续遍历
        if (directories.length === 1 && files.length === 0) {
            return true;
        }

        // 如果有文件或多个目录，根据剩余容量决定
        const remainingCapacity = maxLeafNodes - currentLeafCount;
        const totalItems = files.length + directories.length;

        // 如果剩余容量足够，继续遍历
        return totalItems <= remainingCapacity;

    } catch (error) {
        return false;
    }
}

// 简单的LRU缓存实现
class LRUCache<K, V> {
    private cache = new Map<K, V>();
    private maxSize: number;

    constructor(maxSize: number) {
        this.maxSize = maxSize;
    }

    get(key: K): V | undefined {
        const value = this.cache.get(key);
        if (value !== undefined) {
            // 重新插入以更新顺序
            this.cache.delete(key);
            this.cache.set(key, value);
        }
        return value;
    }

    set(key: K, value: V): void {
        if (this.cache.has(key)) {
            this.cache.delete(key);
        } else if (this.cache.size >= this.maxSize) {
            // 删除最旧的项目
            const firstKey = this.cache.keys().next().value;
            if (firstKey !== undefined) {
                this.cache.delete(firstKey);
            }
        }
        this.cache.set(key, value);
    }
}

// 缓存实例
const buildFileTreeCache = new LRUCache<string, FileNode[]>(128);

export function buildFileTree(rootDir: string, startDir: string, maxLeafNodes: number = 5000, filterMode: FileFilterMode = FileFilterMode.LOCAL): FileNode[] {
    // // 生成缓存键
    // const cacheKey = `${rootDir}|${startDir}|${maxLeafNodes}|${filterMode}`;
    
    // // 检查缓存
    // const cached = buildFileTreeCache.get(cacheKey);
    // if (cached) {
    //     return cached;
    // }

    // 存储结果的根节点列表
    const rootNodes: FileNode[] = [];
    
    // BFS队列：[目录路径, 父节点, 当前深度]
    const queue: [string, FileNode | null, number][] = [[startDir, null, 0]];
    
    // 记录当前叶子节点数量
    let leafCount = 0;

    // 自定义排序函数：目录在前，文件在后，然后按名称字母顺序排序
    const sortKey = (a: Dirent, b: Dirent) => {
        const aIsDirectory = a.isDirectory() ? 0 : 1;
        const bIsDirectory = b.isDirectory() ? 0 : 1;
        if (aIsDirectory !== bIsDirectory) {
            return aIsDirectory - bIsDirectory;
        }
        return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
    };

    while (queue.length > 0 && leafCount < maxLeafNodes) {
        const [currentDir, parentNode, depth] = queue.shift()!;

        try {
            // 获取当前目录下的所有项目并排序
            const items = fs.readdirSync(currentDir, { withFileTypes: true })
                .filter(item => !shouldIgnorePath(path.join(currentDir, item.name), filterMode))
                .sort(sortKey);

            // 分离目录和文件
            const directories = items.filter(item => item.isDirectory());
            const files = items.filter(item => item.isFile());

            // 处理文件（叶子节点）
            for (const fileItem of files) {
                if (leafCount >= maxLeafNodes) {
                    break;
                }

                const filePath = path.join(currentDir, fileItem.name);
                const relativePath = path.relative(rootDir, filePath);
                
                try {
                    const stats = fs.statSync(filePath);
                    const fileNode: FileNode = {
                        id: relativePath,
                        name: fileItem.name,
                        type: 'file',
                        path: relativePath,
                        size: stats.size,
                        lastModified: Math.floor(stats.mtime.getTime() / 1000).toString()
                    };

                    if (parentNode === null) {
                        rootNodes.push(fileNode);
                    } else {
                        if (!parentNode.children) {
                            parentNode.children = [];
                        }
                        parentNode.children.push(fileNode);
                    }

                    leafCount++;
                } catch (error) {
                    // 跳过无法访问的文件
                    continue;
                }
            }

            // 处理目录
            for (const dirItem of directories) {
                if (leafCount >= maxLeafNodes) {
                    break;
                }

                const dirPath = path.join(currentDir, dirItem.name);
                const relativePath = path.relative(rootDir, dirPath);
                const dirNode: FileNode = {
                    id: relativePath,
                    name: dirItem.name,
                    type: 'directory',
                    path: relativePath,
                    children: []
                };

                if (parentNode === null) {
                    rootNodes.push(dirNode);
                } else {
                    if (!parentNode.children) {
                        parentNode.children = [];
                    }
                    parentNode.children.push(dirNode);
                }

                // 检查是否应该继续遍历这个目录
                const shouldTraverse = shouldTraverseDirectory(dirPath, leafCount, maxLeafNodes, filterMode);

                if (shouldTraverse) {
                    queue.push([dirPath, dirNode, depth + 1]);
                } else {
                    // 如果不继续遍历，这个目录就是叶子节点
                    leafCount++;
                }
            }
        } catch (error) {
            // 权限错误时跳过该目录
            continue;
        }
    }

    // 缓存结果
    // buildFileTreeCache.set(cacheKey, rootNodes);
    
    return rootNodes;
}