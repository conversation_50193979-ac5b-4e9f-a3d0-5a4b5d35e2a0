/**
 * XML处理工具函数
 * 
 * 提供安全的XML解析和处理功能，专门解决XML查询中包含特殊字符的问题
 */

import { DOMParser } from '@xmldom/xmldom';

/**
 * 预处理XML查询，智能处理特殊字符
 * 
 * @param xmlQuery 原始XML查询字符串
 * @param queryTag 需要处理的标签名，默认为'query'
 * @returns 处理后的XML查询字符串
 */
export function preprocessXmlQuery(xmlQuery: string, queryTag: string = 'query'): string {
    // 查找指定标签的内容
    const pattern = new RegExp(`<${queryTag}>(.*?)</${queryTag}>`, 's');
    const match = xmlQuery.match(pattern);
    
    if (!match) {
        return xmlQuery;
    }
    
    const tagContent = match[1];
    
    // 如果已经是CDATA格式，不处理
    if (tagContent.includes('<![CDATA[') && tagContent.includes(']]>')) {
        return xmlQuery;
    }
    
    // 如果已经是HTML实体转义，不处理
    if (tagContent.includes('&lt;') || tagContent.includes('&gt;') || tagContent.includes('&amp;')) {
        return xmlQuery;
    }
    
    // 如果包含未转义的<>字符，用CDATA包装
    if (tagContent.includes('<') || tagContent.includes('>')) {
        const cdataContent = `<![CDATA[${tagContent}]]>`;
        return xmlQuery.replace(pattern, `<${queryTag}>${cdataContent}</${queryTag}>`);
    }
    
    return xmlQuery;
}

/**
 * 安全解析XML字符串，自动预处理特殊字符
 * 
 * @param xmlString 要解析的XML字符串
 * @param queryTag 需要预处理的标签名，默认为'query'
 * @returns 解析成功返回Document对象，失败返回null
 */
export function safeParseXmlWithPreprocessing(xmlString: string, queryTag: string = 'query'): Document | null {
    try {
        // 预处理XML
        const processedXml = preprocessXmlQuery(xmlString, queryTag);
        
        // 解析XML
        const parser = new DOMParser();
        const doc = parser.parseFromString(processedXml, 'text/xml');
        
        // 检查是否有解析错误
        const parseError = doc.getElementsByTagName('parsererror');
        if (parseError.length > 0) {
            return null;
        }
        
        return doc;
    } catch (error) {
        return null;
    }
}

/**
 * 从XML元素中提取文本内容，正确处理CDATA和HTML实体
 * 
 * @param element XML元素
 * @returns 提取的文本内容
 */
export function extractTextFromXmlElement(element: Element): string {
    if (!element) {
        return '';
    }
    
    // 获取所有文本内容，包括CDATA
    let textContent = '';
    
    // 遍历所有子节点
    for (let i = 0; i < element.childNodes.length; i++) {
        const node = element.childNodes[i];
        
        if (node.nodeType === 3) { // TEXT_NODE
            textContent += node.nodeValue || '';
        } else if (node.nodeType === 4) { // CDATA_SECTION_NODE
            textContent += node.nodeValue || '';
        }
    }
    
    // 如果没有找到文本内容，尝试获取textContent
    if (!textContent && element.textContent) {
        textContent = element.textContent;
    }
    
    // 处理HTML实体转义
    textContent = textContent
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&apos;/g, "'");
    
    return textContent.trim();
}

/**
 * 安全地从XML字符串中提取多个指定标签的内容，支持预处理
 * 
 * @param xmlString 要解析的XML字符串
 * @param tagNames 要提取的标签名列表
 * @returns 包含(标签名, 标签完整XML内容)的元组列表
 */
export function safeExtractXmlTagsWithPreprocessing(xmlString: string, tagNames: string[]): Array<[string, string]> {
    const results: Array<[string, string]> = [];
    
    try {
        // 首先尝试解析整个XML
        const parser = new DOMParser();
        const doc = parser.parseFromString(xmlString, 'text/xml');
        
        // 检查是否有解析错误
        const parseError = doc.getElementsByTagName('parsererror');
        if (parseError.length === 0) {
            // XML解析成功，使用DOM方法提取
            for (const tagName of tagNames) {
                const elements = doc.getElementsByTagName(tagName);
                
                for (let i = 0; i < elements.length; i++) {
                    const element = elements[i];
                    // 将元素转换回XML字符串
                    const elementXml = new XMLSerializer().serializeToString(element);
                    results.push([tagName, elementXml]);
                }
            }
        } else {
            throw new Error('XML parsing failed');
        }
    } catch (error) {
        // 如果XML解析失败，使用正则表达式作为备选方案
        for (const tagName of tagNames) {
            const pattern = new RegExp(`<${tagName}>(.*?)</${tagName}>`, 'gs');
            let match;
            
            while ((match = pattern.exec(xmlString)) !== null) {
                const fullTag = `<${tagName}>${match[1]}</${tagName}>`;
                results.push([tagName, fullTag]);
            }
        }
    }
    
    return results;
}

/**
 * XMLSerializer的简单实现（如果环境中没有提供）
 */
class XMLSerializer {
    serializeToString(node: Node): string {
        if (node.nodeType === 1) { // ELEMENT_NODE
            const element = node as Element;
            let result = `<${element.tagName}`;
            
            // 添加属性
            if (element.attributes) {
                for (let i = 0; i < element.attributes.length; i++) {
                    const attr = element.attributes[i];
                    result += ` ${attr.name}="${attr.value}"`;
                }
            }
            
            result += '>';
            
            // 添加子节点
            for (let i = 0; i < element.childNodes.length; i++) {
                result += this.serializeToString(element.childNodes[i]);
            }
            
            result += `</${element.tagName}>`;
            return result;
        } else if (node.nodeType === 3) { // TEXT_NODE
            return node.nodeValue || '';
        } else if (node.nodeType === 4) { // CDATA_SECTION_NODE
            return `<![CDATA[${node.nodeValue || ''}]]>`;
        }
        
        return '';
    }
}

// 如果全局环境中没有XMLSerializer，使用我们的实现
if (typeof XMLSerializer === 'undefined') {
    (global as any).XMLSerializer = XMLSerializer;
}