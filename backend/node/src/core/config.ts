import { readFileSync } from 'fs';
import { join } from 'path';
import { parse } from 'yaml';
import { Config } from '../types/config';

let config: Config | null = null;

export function getConfig(): Config {
  if (!config) {
    const env = process.env.ENV || 'dev';
    const configPath = join(__dirname, '../../config/config.yaml');
    const configFile = readFileSync(configPath, 'utf8');
    config = parse(configFile) as Config;
  }
  return config;
}

export function reloadConfig(): void {
  config = null;
  getConfig();
}
