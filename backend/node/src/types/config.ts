export interface LogConfig {
  dir: string;
  name: string;
  level: string;
  max_size_m: number;
}

export interface ApiConfig {
  host: string;
  port: number;
  reload: boolean;
  cors: {
    origins: string[];
    allow_credentials: boolean;
    allow_methods: string[];
    allow_headers: string[];
  };
}

export interface DataConfig {
  repos_path: string;
  ignored_terms_path: string;
}

export interface LLMConfig {
  base_url: string;
  api_key: string;
  model: string;
  temperature: number;
  max_tokens: number;
  stream: boolean;
  timeout: number;
  max_concurrent_requests: number;
}

export interface DeepSearchConfig {
  max_iterations: number;
  max_sub_queries: number;
  max_new_queries: number;
  max_workers: number;
  enabled_search_tools: string[];
  parallel_filter: {
    enabled: boolean;
    min_snippets_for_parallel: number;
    max_workers: number;
    progress_interval: number;
  };
}

export interface FileFilterConfig {
  embedding_exclude: string[];
  embedding_include: string[];
  local_exclude: string[];
  local_include: string[];
  max_file_size: number;
}

export interface ChunkConfig {
  name: string;
  min_chunk_size: number;
  max_chunk_size: number;
  overflow_size: number;
}

export interface DatabaseConfig {
  sqlite: {
    db_path: string;
    timeout: number;
    check_same_thread: boolean;
    isolation_level: null;
  };
}

export interface Config {
  log: LogConfig;
  api: ApiConfig;
  data: DataConfig;
  llm: LLMConfig;
  deepsearch: DeepSearchConfig;
  file_filter: FileFilterConfig;
  chunk: ChunkConfig;
  database: DatabaseConfig;
}
