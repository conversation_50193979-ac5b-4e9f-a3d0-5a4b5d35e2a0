import { InvertedIndexSearchTool } from './inverted_index_search';
import { TermSparseSearch } from './term_sparse_search';
import { GrepSearchTool } from './grep_search';
import { SearchToolEnum } from '../common/constants';
import { getLogger } from '../../utils/trace_logger';
import { Chunk } from '../common/schema';

const logger = getLogger('SearchFactory');

export interface SearchFileResult {
    filePath: string;
    score: number;
    chunks: Chunk[];
}

export interface SearchResult {
    filePath: string;
    score: number;
    chunks: any[];
}

export interface ISearchTool {
    init(): Promise<void>;
    search(query: string, top_k?: number): Promise<SearchResult[]>;
    description?: string;
    examples?: string;
}

export function getSearchTool(repo_id: string, search_tool: SearchToolEnum): ISearchTool {
    switch (search_tool) {
        case SearchToolEnum.INVERTED_INDEX:
            return new InvertedIndexSearchTool(repo_id);
        case SearchToolEnum.EMBEDDING:
            // TODO: Implement EmbeddingSearchTool
            throw new Error('EmbeddingSearchTool not implemented yet');
        case SearchToolEnum.TERM_SPRSE:
            return new TermSparseSearch(repo_id);
        case SearchToolEnum.GREP:
            // Note: GrepSearchTool needs repo_path, but we only have repo_id
            // This is a temporary solution - in practice you'd need to resolve repo_path from repo_id
            return new GrepSearchTool(repo_id, '/tmp');
        default:
            throw new Error(`Unknown search tool: ${search_tool}`);
    }
}