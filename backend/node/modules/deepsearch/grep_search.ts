import { ISearchTool, SearchFileResult } from './search_factory';
import { CodeSnippet, Chunk } from '../common/schema';
import { getConfig } from '../../core/config';
import { getLogger } from '../../utils/trace_logger';
import { shouldIgnorePath } from '../../utils/file';
import { exec } from 'child_process';
import { promisify } from 'util';
import * as path from 'path';

const execAsync = promisify(exec);
const logger = getLogger('GrepSearch');

interface SearchParams {
    path: string;
    regex: string;
    filePattern?: string;
    contextLines?: number;
    maxResults?: number;
}

export class GrepSearchTool implements ISearchTool {
    private repoId: string;
    private repoPath: string;
    private config: any;

    constructor(repoId: string, repoPath: string) {
        this.repoId = repoId;
        this.repoPath = repoPath;
        this.config = getConfig().file_filter;
    }

    async init(): Promise<void> {
        // No initialization needed for grep search
    }

    async search(query: string, maxResults: number = 20): Promise<SearchFileResult[]> {
        try {
            // Parse query parameters
            const searchParams = this.parseQuery(query);
            
            // Build ripgrep command
            const cmd = this.buildRipgrepCommand(searchParams, maxResults);
            logger.info(`CMD: ${cmd}`);

            // Execute search
            const { stdout, stderr } = await execAsync(cmd, {
                cwd: this.repoPath,
                encoding: 'utf8'
            });

            if (stderr && !stderr.includes('No such file or directory')) {
                logger.warning(`Ripgrep stderr: ${stderr}`);
            }

            // Parse ripgrep output
            const snippets = this.parseRipgrepOutput(stdout, searchParams);
            
            // Group snippets by file
            const fileResults = this.groupSnippetsByFile(snippets);
            
            logger.info(`Found ${fileResults.length} files with matches`);
            return fileResults;

        } catch (error) {
            logger.error(`Ripgrep search failed: ${error}`);
            return [];
        }
    }

    private parseQuery(query: string): SearchParams {
        // Simple XML-like parsing for now
        const pathMatch = query.match(/<path>(.*?)<\/path>/s);
        const regexMatch = query.match(/<regex>(.*?)<\/regex>/s);
        const filePatternMatch = query.match(/<file_pattern>(.*?)<\/file_pattern>/s);
        const contextMatch = query.match(/<context>(.*?)<\/context>/s);
        
        const result: SearchParams = {
            path: pathMatch ? pathMatch[1].trim() : '.',
            regex: regexMatch ? regexMatch[1].trim() : query
        };
        
        if (filePatternMatch) {
            result.filePattern = filePatternMatch[1].trim();
        }
        
        if (contextMatch) {
            result.contextLines = parseInt(contextMatch[1].trim(), 10) || 3;
        } else {
            result.contextLines = 3;
        }
        
        return result;
    }

    private buildRipgrepCommand(params: SearchParams, maxResults: number): string {
        const args: string[] = [
            'rg',
            '--json',
            '--line-number',
            '--column',
            '--no-heading',
            '--with-filename',
            `--context=${params.contextLines}`,
            `--max-count=${Math.ceil(maxResults / 5)}`, // Rough estimate
            '--smart-case'
        ];

        // Add file pattern if specified
        if (params.filePattern) {
            args.push('--glob', params.filePattern);
        }

        // Add ignore patterns from config
        if (this.config && this.config.ignore_patterns) {
            for (const pattern of this.config.ignore_patterns) {
                args.push('--glob', `!${pattern}`);
            }
        }

        // Add regex pattern
        args.push(params.regex);
        
        // Add search path
        args.push(params.path);

        return args.join(' ');
    }

    private parseRipgrepOutput(output: string, params: SearchParams): CodeSnippet[] {
        const snippets: CodeSnippet[] = [];
        const lines = output.split('\n').filter(line => line.trim());

        for (const line of lines) {
            try {
                const jsonData = JSON.parse(line);
                
                if (jsonData.type === 'match') {
                    const data = jsonData.data;
                    const filePath = path.resolve(this.repoPath, data.path.text);
                    
                    // Skip ignored paths
                    if (shouldIgnorePath(filePath)) {
                        continue;
                    }

                    const snippet: CodeSnippet = {
                        filePath: filePath,
                        startLine: data.line_number - 1, // Convert to 0-based
                        endLine: data.line_number - 1,
                        content: data.lines.text,
                        contextBefore: '',
                        contextAfter: '',
                        score: 1.0
                    };

                    snippets.push(snippet);
                }
            } catch (error) {
                // Skip invalid JSON lines
                continue;
            }
        }

        return snippets;
    }

    private groupSnippetsByFile(snippets: CodeSnippet[]): SearchFileResult[] {
        const fileMap = new Map<string, CodeSnippet[]>();
        
        for (const snippet of snippets) {
            if (!fileMap.has(snippet.filePath)) {
                fileMap.set(snippet.filePath, []);
            }
            fileMap.get(snippet.filePath)!.push(snippet);
        }

        const results: SearchFileResult[] = [];
        for (const [filePath, fileSnippets] of fileMap) {
            // Calculate average score for the file
            const avgScore = fileSnippets.reduce((sum, s) => sum + s.score, 0) / fileSnippets.length;
            
            // Convert snippets to chunks
            const chunks: Chunk[] = fileSnippets.map(snippet => ({
                filePath: snippet.filePath,
                startLine: snippet.startLine,
                endLine: snippet.endLine,
                content: snippet.content
            }));

            results.push({
                filePath,
                score: avgScore,
                chunks
            });
        }

        return results.sort((a, b) => b.score - a.score);
    }

    get description(): string {
        return `- \`grep\`: Request to perform a regex search across files in a specified directory, providing context-rich results. This tool searches for patterns or specific content across multiple files, displaying each match with encapsulating context.
**Parameters**:
- path: (required) The path of the directory to search in (relative to the current workspace directory. This directory will be recursively searched.
- query: (required) The regular expression pattern to search for. Uses Rust regex syntax.
- file_pattern: (optional) Glob pattern to filter files (e.g., '*.ts' for TypeScript files). If not provided, it will search all files (*).

**Usage**:
<search_files>
<path>Directory path here</path>
<query>Your regex pattern here</query>
<file_pattern>file pattern here (optional)</file_pattern>
</search_files>

**Performance Tips:**
- Use specific paths instead of searching entire repositories
- Use file_pattern to limit search scope by language
- Prefer simple patterns over complex multi-line regex when possible`;
    }

    get examples(): string {
        return `<output>
    <grep>
    <path>.</path>
    <query>.*</query>
    <file_pattern>*.ts</file_pattern>
    </grep>
</output>`;
    }
}