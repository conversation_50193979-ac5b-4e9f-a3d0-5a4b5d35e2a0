import { ISearchTool } from './search_factory';
import { CodeSnippet, Chunk } from '../common/schema';
import { getTerms } from '../term/term';
import { getChunkService } from '../chunks/chunk_factory';
import { getSqliteClient } from '../integrations/database/sqlite/client';
import { getConfig } from '../../core/config';
import { FileType } from '../../utils/file';
import { getLogger } from '../../utils/trace_logger';
import * as fs from 'fs';

const logger = getLogger('TermSparseSearch');

export class TermSparseSearch implements ISearchTool {
    private repoId: string;
    private config: any;

    constructor(repoId: string) {
        this.repoId = repoId;
        this.config = getConfig().deepsearch;
    }

    async init(): Promise<void> {
        // No initialization needed for term sparse search
    }

    async search(query: string, topK: number = 20): Promise<any[]> {
        try {
            // Parse query parameters
            const searchParams = this.parseQuery(query);
            const actualQuery = searchParams.query || query;
            const fileType = searchParams.fileType || 'code';
            const actualTopK = searchParams.topK || topK;

            logger.info(`Term sparse search for query: ${actualQuery}, file_type: ${fileType}, top_k: ${actualTopK}`);

            // Get repository information
            const client = getSqliteClient();
            // 如果 repoId 是数字字符串，使用 getRepoById，否则使用 getRepo
            const repo = /^\d+$/.test(this.repoId) 
                ? await client.getRepoById(parseInt(this.repoId, 10))
                : await client.getRepo(this.repoId);
            if (!repo) {
                throw new Error(`Repository not found: ${this.repoId}`);
            }

            // Get query terms
            const queryTerms = getTerms(actualQuery, fileType as FileType);
            const queryTermsArray = Array.from(queryTerms);
            if (queryTermsArray.length === 0) {
                logger.warning('No terms extracted from query');
                return [];
            }

            // Calculate BM25 scores for chunks
            const chunkScores = await this.calculateBM25ChunkTerms(
                queryTermsArray,
                this.repoId,
                fileType as FileType
            );

            // Sort by score and take top K
            const sortedChunks = chunkScores
                .sort((a, b) => b.score - a.score)
                .slice(0, actualTopK);

            // Convert to search results
            const results: any[] = [];
            for (const chunkScore of sortedChunks) {
                const chunk = chunkScore.chunk;
                const content = await this.readChunkContents(chunk.filePath, chunk.startLine, chunk.endLine);
                
                const searchResult = {
                    filePath: chunk.filePath,
                    score: chunkScore.score,
                    chunks: [{
                        filePath: chunk.filePath,
                        startLine: chunk.startLine,
                        endLine: chunk.endLine,
                        content: content
                    }]
                };
                results.push(searchResult);
            }

            logger.info(`Found ${results.length} results`);
            return results;

        } catch (error) {
            logger.error(`Term sparse search failed: ${error}`);
            return [];
        }
    }

    private parseQuery(query: string): { query?: string; fileType?: string; topK?: number } {
        // Simple XML-like parsing for now
        // In a full implementation, you would use a proper XML parser
        const queryMatch = query.match(/<query>(.*?)<\/query>/s);
        const fileTypeMatch = query.match(/<file_type>(.*?)<\/file_type>/s);
        const topKMatch = query.match(/<top_k>(.*?)<\/top_k>/s);
        
        const result: { query?: string; fileType?: string; topK?: number } = {};
        
        if (queryMatch) {
            result.query = queryMatch[1].trim();
        } else {
            result.query = query;
        }
        
        if (fileTypeMatch) {
            result.fileType = fileTypeMatch[1].trim();
        }
        
        if (topKMatch) {
            result.topK = parseInt(topKMatch[1].trim(), 10);
        }
        
        return result;
    }

    private async calculateBM25ChunkTerms(
        queryTerms: string[],
        repoId: string,
        fileType: FileType
    ): Promise<Array<{ chunk: Chunk; score: number }>> {
        // This is a simplified BM25 implementation
        // In a real implementation, you would need to:
        // 1. Get all chunks for the repository
        // 2. Calculate term frequencies for each chunk
        // 3. Calculate inverse document frequencies
        // 4. Apply BM25 formula
        
        // For now, return empty array as placeholder
        return [];
    }

    private async readChunkContents(filePath: string, startLine: number, endLine: number): Promise<string> {
        try {
            const content = await fs.promises.readFile(filePath, 'utf-8');
            const lines = content.split('\n');
            return lines.slice(startLine, endLine + 1).join('\n');
        } catch (error) {
            return '';
        }
    }

    get description(): string {
        return `- \`term_sparse\`: BM25-based semantic search engine for fuzzy functionality discovery. Designed for scenarios where you don't have specific keywords or need to find code/documentation by describing general functionality or content themes.

**Primary Use Cases**:
1. **No Specific Keywords**: When you don't know exact function names, class names, or technical terms to search for
2. **Functionality-Based Search**: When you want to find code that performs certain functions or documentation covering specific topics, described in general terms

**Key Features**:
- Statistical relevance ranking using BM25 algorithm
- Handles vague or general descriptions by analyzing term patterns
- Works with both code functionality descriptions and documentation content themes
- Returns results ranked by semantic similarity rather than exact matches

**When to Use This Tool**:
- ✅ Use when you want to find chunks with similar term patterns to your query
- ✅ Query should mimic the actual content structure of target chunks
- ✅ For code: use concise pseudo-code that represents the target code structure
- ✅ For docs: use representative text snippets that match documentation style
- ❌ Don't use descriptive language about what you're looking for

**Parameters**:
- query: Content that mimics the structure and terms of target chunks
- file_type: (optional) "code" (default) for source code, "doc" for documentation
- top_k: (optional) Maximum results to return (default: 20)

**Query Style**: Write content that structurally resembles what you expect to find, using similar terms and patterns as the target chunks.`;
    }

    get examples(): string {
        return `**Example Queries**:

1. **Feature-based Search**:
   - \`user authentication login\` - Find authentication and login related code
   - \`database connection pool\` - Find database connection pooling implementations
   - \`file upload validation\` - Find file upload and validation logic

2. **Component Search**:
   - \`react component button\` - Find React button components
   - \`express middleware cors\` - Find Express CORS middleware
   - \`vue router navigation\` - Find Vue.js routing and navigation code

3. **API and Service Search**:
   - \`rest api endpoint\` - Find REST API endpoint definitions
   - \`graphql resolver mutation\` - Find GraphQL mutation resolvers
   - \`microservice communication\` - Find inter-service communication code

4. **Data Processing Search**:
   - \`json parsing validation\` - Find JSON parsing and validation code
   - \`csv export import\` - Find CSV import/export functionality
   - \`image resize compression\` - Find image processing code

5. **Security and Error Handling**:
   - \`jwt token verification\` - Find JWT token verification logic
   - \`error handling middleware\` - Find error handling middleware
   - \`input sanitization xss\` - Find XSS prevention and input sanitization

6. **Testing and Configuration**:
   - \`unit test mock\` - Find unit tests with mocking
   - \`environment config production\` - Find production configuration
   - \`logging debug trace\` - Find logging and debugging code

**Query Tips**:
- Use 2-4 descriptive terms for optimal results
- Combine technical terms with functional descriptions
- Include technology stack terms when relevant (react, express, etc.)`;
    }
}