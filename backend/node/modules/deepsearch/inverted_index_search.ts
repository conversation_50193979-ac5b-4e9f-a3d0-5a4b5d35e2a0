import { getLogger } from '../../utils/trace_logger';
import { getSqliteClient, Repo } from '../integrations/database/sqlite/client';
import { getTerms, getFileType } from '../term/term';
import { Counter } from '../../utils/counter';
import { Chunk } from '../common/schema';
import { getChunkService } from '../chunks/chunk_factory';
import { FileType } from '../../utils/file';
import { SearchResult } from './search_factory';
import * as fs from 'fs';
import * as path from 'path';

const logger = getLogger('InvertedIndexSearchTool');



export class InvertedIndexSearchTool {
    private repo_id: string;
    private repo: Repo | null = null;
    private inverted_index: { [key: string]: { count: number, files: string[] } } = {};

    constructor(repo_id: string) {
        this.repo_id = repo_id;
    }

    async init() {
        const sqliteClient = getSqliteClient();
        // 如果 repo_id 是数字字符串，使用 getRepoById，否则使用 getRepo
        if (/^\d+$/.test(this.repo_id)) {
            this.repo = await sqliteClient.getRepoById(parseInt(this.repo_id, 10));
        } else {
            this.repo = await sqliteClient.getRepo(this.repo_id);
            // 如果仓库不存在，创建一个新的
            if (!this.repo) {
                this.repo = await sqliteClient.createRepo(this.repo_id);
            }
        }
        if (this.repo && this.repo.inverted_index) {
            this.inverted_index = JSON.parse(this.repo.inverted_index);
        }
    }

    public async search(query: string, top_k: number = 10): Promise<SearchResult[]> {
        if (!this.repo) {
            logger.warn('Repository not initialized');
            return [];
        }

        // Extract terms from query
        const query_terms = getTerms(query, FileType.CODE);
        logger.info(`Query terms: ${query_terms.join(', ')}`);

        // Calculate file scores based on term frequency
        const file_scores: { [file_path: string]: number } = {};
        
        for (const term of query_terms) {
            if (term in this.inverted_index) {
                const term_data = this.inverted_index[term];
                for (const file_path of term_data.files) {
                    if (!(file_path in file_scores)) {
                        file_scores[file_path] = 0;
                    }
                    // Simple TF-IDF like scoring
                    file_scores[file_path] += 1 / Math.log(term_data.files.length + 1);
                }
            }
        }

        // Sort files by score
        const sorted_files = Object.entries(file_scores)
            .sort((a, b) => b[1] - a[1])
            .slice(0, top_k);

        // Generate chunks for top files
        const results: SearchResult[] = [];
        const chunk_service = getChunkService('line');

        for (const [file_path, score] of sorted_files) {
            try {
                const full_path = path.join(this.repo.repo_path, file_path);
                const content = fs.readFileSync(full_path, 'utf-8');
                const [key_structure, chunks] = chunk_service.chunk_file(file_path, content);
                
                results.push({
                    filePath: file_path,
                    score,
                    chunks
                });
            } catch (e) {
                logger.warn(`Failed to read file ${file_path}: ${e}`);
                continue;
            }
        }

        return results;
    }

    get description(): string {
        return `- \`inverted_index\`: Fast keyword-based search using inverted index. Optimized for exact keyword matching and multi-term queries with boolean logic.

**Primary Use Cases**:
1. **Exact Keyword Search**: When you know specific function names, class names, or variable names
2. **Multi-term Queries**: Combining multiple keywords with AND/OR logic
3. **Fast Retrieval**: When you need quick results for known terms

**Key Features**:
- Pre-built inverted index for instant keyword lookup
- Boolean query support (AND, OR, NOT operations)
- Term frequency scoring for relevance ranking
- Efficient for large codebases

**When to Use This Tool**:
- ✅ Use when you have specific keywords or identifiers to search for
- ✅ Perfect for finding function definitions, class declarations, variable usage
- ✅ Ideal for multi-keyword searches with logical operators
- ❌ Not suitable for fuzzy or semantic searches

**Parameters**:
- query: Keywords or terms to search for
- top_k: (optional) Maximum results to return (default: 10)

**Query Style**: Use specific keywords, function names, class names, or combine terms with spaces for AND logic.`;
    }

    get examples(): string {
        return `**Example Queries**:

1. **Function Search**:
   - \`authenticate user\` - Find authentication-related functions
   - \`validate password\` - Find password validation code
   - \`connect database\` - Find database connection logic

2. **Class/Type Search**:
   - \`UserModel\` - Find UserModel class definition
   - \`HttpClient\` - Find HTTP client implementations
   - \`ConfigManager\` - Find configuration management classes

3. **Variable/Constant Search**:
   - \`API_KEY\` - Find API key usage
   - \`DATABASE_URL\` - Find database URL references
   - \`MAX_RETRIES\` - Find retry limit constants

4. **Multi-term Queries**:
   - \`error handling middleware\` - Find error handling middleware
   - \`jwt token validation\` - Find JWT token validation code
   - \`file upload service\` - Find file upload service implementations`;
    }
}