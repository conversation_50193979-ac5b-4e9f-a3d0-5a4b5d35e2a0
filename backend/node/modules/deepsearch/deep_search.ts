import { LLMClient, defaultLlmClient } from '../llm/llm_client';
import { SearchResult, CodeSnippet, SearchQuery } from '../common/schema';
import { SearchToolEnum, FileFilterMode } from '../common/constants';
import { getConfig } from '../../core/config';
import { SUBQUERY_REORDER_PROMPT, GENERATE_NEW_QUERY_PROMPT, SYSTEM_PROMPTS, QUERY_SPLIT_PROMPT } from './prompts';
import { getLogger } from '../../utils/trace_logger';
import { buildFileTree, FileNode } from '../../utils/file';
import { getSearchTool, ISearchTool } from './search_factory';

const trace_logger = getLogger(__filename);

export class DeepSearch {
    private repoPath: string;
    private repoId: string;
    private repoInfo: string;
    private llmClient: LLMClient;
    private searchTool: SearchToolEnum;
    private searchInstance: ISearchTool;
    private config: any;

    constructor(repoPath: string, repoId: string, repoInfo: string = '', llmClient: LLMClient | null = null, searchTool: SearchToolEnum = SearchToolEnum.INVERTED_INDEX) {
        this.repoPath = repoPath;
        this.repoId = repoId;
        this.repoInfo = repoInfo;
        this.searchTool = searchTool;
        this.searchInstance = getSearchTool(repoId, searchTool);
        this.llmClient = llmClient || defaultLlmClient;
        this.config = getConfig().deepsearch;
    }

    async init(): Promise<void> {
        await this.searchInstance.init();
    }

    async searchAsync(query: string): Promise<SearchResult> {
        const result: SearchResult = { originalQuery: query, allQueries: [], codeSnippets: [], iterations: 0 };
        trace_logger.info(`Start Async DeepSearch for Query: ${query}`);

        const repoNode: FileNode = {
            id: this.repoPath,
            path: this.repoPath,
            name: this.repoPath.split('/').pop() || '',
            type: 'directory',
            children: buildFileTree(this.repoPath, this.repoPath, 100, FileFilterMode.LOCAL)
        };

        try {
            const searchResults = await this.searchInstance.search(query, this.config.max_results || 10);
            const codeSnippets = this._convertSearchResultsToSnippets(searchResults);
            
            result.allQueries.push({ text: query, context_operations: [] });
            result.codeSnippets = codeSnippets;
            result.iterations = 1;
            
        } catch (e) {
            trace_logger.error(`Search failed: ${e}`);
        }

        return result;
    }

    private _convertSearchResultsToSnippets(searchResults: any[]): CodeSnippet[] {
        const codeSnippets: CodeSnippet[] = [];
        for (const searchResult of searchResults) {
            for (const chunk of searchResult.chunks) {
                codeSnippets.push({
                    filePath: chunk.filePath,
                    startLine: chunk.startLine,
                    endLine: chunk.endLine,
                    content: chunk.content,
                    contextBefore: '',
                    contextAfter: '',
                    score: searchResult.score
                });
            }
        }
        return codeSnippets;
    }

    // Additional methods can be added here for query splitting and other functionality
    private async _split_query_async(query: string, repoNode: FileNode, maxSubQueries: number): Promise<string[]> {
        // Placeholder implementation - can be enhanced with LLM integration
        return [query];
    }
}