import path from 'path';
import { Sequelize, DataTypes, Model, ModelCtor } from 'sequelize';
import { getLogger } from '../../../../utils/trace_logger';

const logger = getLogger('sqlite');

class Repo extends Model {
    public repo_id!: number;
    public repo_path!: string;
    public inverted_index!: string | null;
    public term_sparse!: string | null;
}

class RepoStructure extends Model {
    public id!: number;
    public repo_id!: number;
    public file_path!: string;
    public parent_path!: string;
    public file_type!: string;
    public file_size!: number;
    public last_modified!: Date;
}

class SqliteClient {
    private static instance: SqliteClient;
    private sequelize: Sequelize;
    private RepoModel: ModelCtor<Repo>;
    private RepoStructureModel: ModelCtor<RepoStructure>;

    private constructor() {
        const dbPath = path.join(process.cwd(), 'db.sqlite');
        this.sequelize = new Sequelize({
            dialect: 'sqlite',
            storage: dbPath,
            logging: (msg: string) => logger.info(msg),
        });

        this.RepoModel = this.sequelize.define<Repo>('Repo', {
            repo_id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            repo_path: {
                type: DataTypes.STRING,
                unique: true,
                allowNull: false,
            },
            inverted_index: {
                type: DataTypes.TEXT,
                allowNull: true,
            },
            term_sparse: {
                type: DataTypes.TEXT,
                allowNull: true,
            },
        });

        this.RepoStructureModel = this.sequelize.define<RepoStructure>('RepoStructure', {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            repo_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: this.RepoModel,
                    key: 'repo_id',
                },
            },
            file_path: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            parent_path: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            file_type: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            file_size: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            last_modified: {
                type: DataTypes.DATE,
                allowNull: false,
            },
        }, {
            indexes: [
                {
                    unique: true,
                    fields: ['repo_id', 'file_path'],
                },
            ],
        });

        this.sequelize.sync();
    }

    public static getInstance(): SqliteClient {
        if (!SqliteClient.instance) {
            SqliteClient.instance = new SqliteClient();
        }
        return SqliteClient.instance;
    }

    public async getRepo(repoPath: string): Promise<Repo | null> {
        return await this.RepoModel.findOne({ where: { repo_path: repoPath } });
    }

    public async getRepoById(repoId: number): Promise<Repo | null> {
        return await this.RepoModel.findOne({ where: { repo_id: repoId } });
    }

    public async updateRepo(repo: Repo, fields: string[]): Promise<void> {
        await repo.save({ fields });
    }

    public async createRepo(repoPath: string): Promise<Repo> {
        return await this.RepoModel.create({
            repo_path: repoPath,
            inverted_index: null,
            term_sparse: null
        });
    }

    public async upsertRepositoryKeyStructure(repoId: number, structures: any[]): Promise<void> {
        for (const structure of structures) {
            await this.RepoStructureModel.upsert({
                repo_id: repoId,
                file_path: structure.file_path,
                parent_path: structure.parent_path,
                file_type: structure.file_type,
                file_size: structure.file_size,
                last_modified: structure.last_modified,
            });
        }
    }
}

export function getSqliteClient(): SqliteClient {
    return SqliteClient.getInstance();
}

export { Repo };