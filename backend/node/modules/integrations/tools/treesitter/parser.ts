import Parser from 'tree-sitter';
import fs from 'fs';
import path from 'path';
import { Chunk } from '../../../common/schema';
import { LanguageEnum, languageEnumFromSuffix } from '../../../common/constants';
import { ChunkConfig } from '../../../../core/config';
import { BaseLineChunk } from '../../../chunks/base_line_chunk';

export class FileParser {
    private languageParsers: Map<LanguageEnum, Parser> = new Map();
    private languageQueries: Map<LanguageEnum, Parser.Query> = new Map();
    private config: ChunkConfig;

    constructor(chunkConfig: ChunkConfig) {
        this.config = chunkConfig;
    }

    public parse(filePath: string, fileContent: string): [string, Chunk[]] {
        const fileExtension = filePath.split('.').pop();
        if (!fileExtension) {
            return ['', []];
        }

        const languageEnum = languageEnumFromSuffix(fileExtension);
        if (!languageEnum) {
            console.warn(`Unsupported file extension: ${fileExtension}`);
            return new BaseLineChunk().chunk_file(filePath, fileContent);
        }

        const parser = this.getOrCreateParser(languageEnum);
        if (!parser) {
            console.warn(`Failed to get parser for ${languageEnum}`);
            return new BaseLineChunk().chunk_file(filePath, fileContent);
        }

        const query = this.getOrCreateQuery(languageEnum, parser);
        if (!query) {
            console.warn(`Failed to get query for ${languageEnum}`);
            return new BaseLineChunk().chunk_file(filePath, fileContent);
        }

        const tree = parser.parse(fileContent);
        const lines = fileContent.split('\n');
        const fileChunks: Chunk[] = [];
        const keyStructureLines = new Map<number, [number, string]>();

        const captures = query.captures(tree.rootNode);
        const definitions = captures
            .filter(capture => capture.name.includes('definition'))
            .sort((a, b) => {
                if (a.node.startPosition.row !== b.node.startPosition.row) {
                    return a.node.startPosition.row - b.node.startPosition.row;
                }
                return b.node.endPosition.row - a.node.endPosition.row;
            });

        let seenStartLines = new Set<number>();
        let seenMinStartLine = 0;
        let seenMaxEndLine = 0;
        let prevNodes: Parser.SyntaxNode[] = [];

        for (const { node } of definitions) {
            if (seenStartLines.has(node.startPosition.row)) {
                continue;
            }
            seenStartLines.add(node.startPosition.row);

            // Record key structure lines
            if (node.type.includes('definition')) {
                keyStructureLines.set(node.startPosition.row, [node.startPosition.column, lines[node.startPosition.row]]);
                keyStructureLines.set(node.endPosition.row, [node.endPosition.column, lines[node.endPosition.row]]);
            }

            if (node.startPosition.row >= seenMinStartLine && node.endPosition.row <= seenMaxEndLine) {
                continue;
            }
            seenMinStartLine = Math.min(seenMinStartLine, node.startPosition.row);
            seenMaxEndLine = Math.max(seenMaxEndLine, node.endPosition.row);

            const nodeLineCount = node.endPosition.row - node.startPosition.row + 1;

            // Handle large nodes
            if (nodeLineCount > this.config.max_chunk_size) {
                // Clear previous nodes
                if (prevNodes.length > 0) {
                    fileChunks.push({
                        filePath,
                        startLine: prevNodes[0].startPosition.row,
                        endLine: prevNodes[prevNodes.length - 1].endPosition.row,
                        content: lines.slice(prevNodes[0].startPosition.row, prevNodes[prevNodes.length - 1].endPosition.row + 1).join('\n')
                    });
                    prevNodes = [];
                }

                // Add current large node
                fileChunks.push({
                    filePath,
                    startLine: node.startPosition.row,
                    endLine: node.endPosition.row,
                    content: lines.slice(node.startPosition.row, node.endPosition.row + 1).join('\n')
                });

                // Split large node into segments
                for (let i = node.startPosition.row; i < node.endPosition.row; i += this.config.max_chunk_size) {
                    const startLine = i;
                    const endLine = Math.min(i + this.config.max_chunk_size, node.endPosition.row);
                    let content = lines.slice(startLine, endLine + 1).join('\n');

                    if (i !== node.startPosition.row) {
                        content = lines[node.startPosition.row] + '\n...\n' + content;
                    }
                    if (endLine !== node.endPosition.row) {
                        content += '\n...\n' + lines[node.endPosition.row];
                    }

                    fileChunks.push({
                        filePath,
                        startLine,
                        endLine,
                        content
                    });
                }
                continue;
            }

            // Handle normal sized nodes
            if (prevNodes.length === 0) {
                if (nodeLineCount >= this.config.min_chunk_size && nodeLineCount <= this.config.max_chunk_size) {
                    fileChunks.push({
                        filePath,
                        startLine: node.startPosition.row,
                        endLine: node.endPosition.row,
                        content: lines.slice(node.startPosition.row, node.endPosition.row + 1).join('\n')
                    });
                } else {
                    prevNodes.push(node);
                }
                continue;
            }

            // Handle nodes with previous context
            if (node.endPosition.row - prevNodes[0].startPosition.row >= this.config.min_chunk_size) {
                if (node.endPosition.row - prevNodes[0].startPosition.row <= this.config.max_chunk_size) {
                    fileChunks.push({
                        filePath,
                        startLine: prevNodes[0].startPosition.row,
                        endLine: node.endPosition.row,
                        content: lines.slice(prevNodes[0].startPosition.row, node.endPosition.row + 1).join('\n')
                    });
                    prevNodes = [];
                } else {
                    fileChunks.push({
                        filePath,
                        startLine: prevNodes[0].startPosition.row,
                        endLine: prevNodes[prevNodes.length - 1].endPosition.row,
                        content: lines.slice(prevNodes[0].startPosition.row, prevNodes[prevNodes.length - 1].endPosition.row + 1).join('\n')
                    });
                    prevNodes = [node];
                }
            } else {
                prevNodes.push(node);
            }
        }

        // Handle remaining nodes
        if (prevNodes.length > 0) {
            fileChunks.push({
                filePath,
                startLine: prevNodes[0].startPosition.row,
                endLine: prevNodes[prevNodes.length - 1].endPosition.row,
                content: lines.slice(prevNodes[0].startPosition.row, prevNodes[prevNodes.length - 1].endPosition.row + 1).join('\n')
            });
        }

        // Generate key structure
        if (keyStructureLines.size === 0) {
            for (const chunk of fileChunks) {
                keyStructureLines.set(chunk.startLine, [0, lines[chunk.startLine]]);
                keyStructureLines.set(chunk.endLine, [0, lines[chunk.endLine]]);
            }
        }

        const keyStructure = Array.from(keyStructureLines.entries())
            .sort(([a], [b]) => a - b)
            .map(([lineIdx, [colIdx, lineContent]], idx, arr) => {
                let content = `L${lineIdx}: ${lineContent}`;
                if (idx > 0 && lineIdx - arr[idx - 1][0] > 1) {
                    content = '\t' + ' '.repeat(colIdx) + '...\n' + content;
                }
                return content;
            })
            .join('\n');

        return [keyStructure, fileChunks];
    }

    private getOrCreateParser(language: LanguageEnum): Parser | undefined {
        if (this.languageParsers.has(language)) {
            return this.languageParsers.get(language);
        }

        try {
            const Language = require(`tree-sitter-${language}`);
            const parser = new Parser();
            parser.setLanguage(Language);
            this.languageParsers.set(language, parser);
            return parser;
        } catch (e) {
            console.error(`Failed to load parser for ${language}`, e);
            return undefined;
        }
    }

    private getOrCreateQuery(language: LanguageEnum, parser: Parser): Parser.Query | undefined {
        if (this.languageQueries.has(language)) {
            return this.languageQueries.get(language);
        }

        try {
            const queryPath = path.join(__dirname, 'queries', `${language}.scm`);
            const queryContent = fs.readFileSync(queryPath, 'utf8');
            const query = parser.getLanguage().query(queryContent);
            this.languageQueries.set(language, query);
            return query;
        } catch (e) {
            console.error(`Failed to load query for ${language}`, e);
            return undefined;
        }
    }
}