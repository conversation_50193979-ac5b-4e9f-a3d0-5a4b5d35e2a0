import { FileIOTool } from './file_io';
import { DirectoryIOTool } from './directory_io';
import { getLogger } from '../../../../utils/trace_logger';

const trace_logger = getLogger(__filename);

export class AnyIOTool {
    private fileIo: FileIOTool;
    private directoryIo: DirectoryIOTool;

    constructor(repoPath: string) {
        this.fileIo = new FileIOTool(repoPath);
        this.directoryIo = new DirectoryIOTool(repoPath);
    }

    read(operation: any): string {
        /**
         * 统一的读取接口，根据操作类型调用相应的工具
         */
        try {
            if (operation.file_io) {
                return this.fileIo.read(operation.file_io);
            } else if (operation.directory_io) {
                return this.directoryIo.read(operation.directory_io);
            } else {
                return 'Error: Unknown operation type';
            }
        } catch (error: any) {
            trace_logger.error(`IO操作失败: ${error.message}`);
            return `Error: ${error.message}`;
        }
    }

    get availableTools(): string {
        return `Available tools:
${this.fileIo.description}\n\n${this.directoryIo.description}`;
    }

    get toolsExamples(): { file_io: string; directory_io: string } {
        return {
            file_io: this.fileIo.examples,
            directory_io: this.directoryIo.examples
        };
    }
}