import * as fs from 'fs';
import * as path from 'path';
import { buildFileTree, FileNode } from '../../../../utils/file';
import { FileFilterMode } from '../../../common/constants';
import { getLogger } from '../../../../utils/trace_logger';

const trace_logger = getLogger(__filename);

export class DirectoryIOTool {
    private repoPath: string;

    constructor(repoPath: string) {
        this.repoPath = repoPath;
    }

    read(query: string): string {
        /**
         * 读取目录结构
         */
        const params = this._parseQuery(query);

        if (!params.path) {
            return 'Error: No directory path specified';
        }

        try {
            const fileTree = this._readDirStructure(
                params.path,
                params.maxFiles
            );

            return this._formatFileTree(fileTree);
        } catch (error: any) {
            if (error.code === 'ENOENT') {
                return `Error: Directory '${params.path}' not found`;
            }
            return `Error reading directory: ${error.message}`;
        }
    }

    private _parseQuery(query: string): {
        path: string;
        maxFiles: number;
    } {
        const params = {
            path: '',
            maxFiles: 100
        };

        try {
            // 简单的XML解析
            const pathMatch = query.match(/<path>([^<]+)<\/path>/);
            const maxFilesMatch = query.match(/<max_files>(\d+)<\/max_files>/);

            if (pathMatch) {
                params.path = pathMatch[1].trim();
            }
            if (maxFilesMatch) {
                params.maxFiles = parseInt(maxFilesMatch[1], 10);
            }
        } catch (error) {
            trace_logger.error(`解析查询参数失败: ${error}`);
        }

        return params;
    }

    private _readDirStructure(dirPath: string, maxFiles: number): FileNode[] {
        // 构建完整路径
        const fullPath = path.isAbsolute(dirPath) ? dirPath : path.join(this.repoPath, dirPath);
        
        // 检查目录是否存在
        if (!fs.existsSync(fullPath)) {
            throw new Error(`Directory not found: ${fullPath}`);
        }

        // 使用buildFileTree函数构建文件树
        const fileTree = buildFileTree(fullPath, fullPath, maxFiles, FileFilterMode.LOCAL);
        return fileTree || [];
    }

    private _formatFileTree(nodes: FileNode[], depth: number = 0): string {
        const result: string[] = [];
        const indent = '  '.repeat(depth);

        for (const node of nodes) {
            const suffix = node.type === 'directory' ? '/' : '';
            result.push(`${indent}${node.name}${suffix}`);
            
            if (node.children && node.children.length > 0) {
                result.push(this._formatFileTree(node.children, depth + 1));
            }
        }

        return result.join('\n');
    }

    get description(): string {
        return `- \`directory_io\`: Directory I/O operations tool for reading directory content.

  Parameters:
  - \`path\` (required): Relative directory path from repository root
  - \`max_files\` (optional): Maximum number of files to return (default: 100)

  Use cases:
  - Explore directory structure and contents
  - Identify files of interest for further analysis`;
    }

    get examples(): string {
        return `<directory_io>
    <path>src</path>
    <max_files>100</max_files>
</directory_io>`;
    }
}