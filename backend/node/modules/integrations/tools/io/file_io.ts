import * as fs from 'fs';
import * as path from 'path';
import { getLogger } from '../../../../utils/trace_logger';

const trace_logger = getLogger(__filename);

export class FileIOTool {
    private repoPath: string;

    constructor(repoPath: string) {
        this.repoPath = repoPath;
    }

    read(query: string): string {
        /**
         * 读取文件内容
         */
        const params = this._parseQuery(query);

        if (!params.path) {
            return 'Error: No file path specified';
        }

        try {
            return this._readFile(
                params.path,
                params.startLine,
                params.endLine
            );
        } catch (error: any) {
            if (error.code === 'ENOENT') {
                return `Error: File '${params.path}' not found`;
            }
            return `Error reading file: ${error.message}`;
        }
    }

    private _parseQuery(query: string): {
        path: string;
        startLine: number;
        endLine: number;
    } {
        const params = {
            path: '',
            startLine: 0,
            endLine: 200
        };

        try {
            // 简单的XML解析
            const pathMatch = query.match(/<path>([^<]+)<\/path>/);
            const startLineMatch = query.match(/<start_line>(\d+)<\/start_line>/);
            const endLineMatch = query.match(/<end_line>(\d+)<\/end_line>/);

            if (pathMatch) {
                params.path = pathMatch[1].trim();
            }
            if (startLineMatch) {
                params.startLine = parseInt(startLineMatch[1], 10);
            }
            if (endLineMatch) {
                params.endLine = parseInt(endLineMatch[1], 10);
            }
        } catch (error) {
            trace_logger.error(`解析查询参数失败: ${error}`);
        }

        return params;
    }

    private _readFile(filePath: string, startLine: number = 0, endLine: number = 200): string {
        // 构建完整路径
        const fullPath = path.isAbsolute(filePath) ? filePath : path.join(this.repoPath, filePath);
        
        // 检查文件是否存在
        if (!fs.existsSync(fullPath)) {
            throw new Error(`File not found: ${fullPath}`);
        }

        // 读取文件内容
        const content = fs.readFileSync(fullPath, 'utf8');
        const lines = content.split('\n');

        // 处理行号范围
        const actualStartLine = Math.max(0, startLine);
        const actualEndLine = Math.min(lines.length - 1, endLine);

        if (actualStartLine > actualEndLine) {
            return '';
        }

        // 返回指定行范围的内容
        const selectedLines = lines.slice(actualStartLine, actualEndLine + 1);
        return selectedLines.join('\n');
    }

    get description(): string {
        return `- \`file_io\`: File I/O operations tool for reading file content.

  Parameters:
  - \`path\` (required): Relative file path from repository root
  - \`start_line\` (optional): Starting line number for reading (default: 0, 0-based indexing)
  - \`end_line\` (optional): Ending line number for reading (default: 200, inclusive)

  Use cases:
  - Read entire files or specific line ranges for code analysis
  - Inspect configuration files, source code, or documentation`;
    }

    get examples(): string {
        return `<file_io>
    <path>src/main.ts</path>
    <start_line>0</start_line>
    <end_line>9</end_line>
</file_io>

<file_io>
    <path>src/main.ts</path>
</file_io>`;
    }
}