import { spawn } from 'child_process';
import { getLogger } from '../../../../utils/trace_logger';
import { CodeSnippet } from '../../../common/schema';
import { SearchToolABC } from './abc_search';
import { getConfig } from '../../../../core/config';
import { shouldIgnorePath } from '../../../../utils/file';

const logger = getLogger(__filename);

interface SearchParams {
    pattern: string;
    caseSensitive: boolean;
    wholeWord: boolean;
    regex: boolean;
    maxContextLines: number;
    maxResults: number;
}

export class GrepSearchTool extends SearchToolABC {
    constructor(public repoPath: string) {
        super(repoPath);
    }

    async search(query: string, ...kwargs: any[]): Promise<CodeSnippet[]> {
        try {
            const searchParams = this._parseQuery(query);
            const cmd = this._buildRipgrepCommand(searchParams);
            logger.info(`CMD: ${cmd.join(' ')}`);

            return new Promise((resolve, reject) => {
                const process = spawn(cmd[0], cmd.slice(1), {
                    cwd: this.repoPath,
                    stdio: ['ignore', 'pipe', 'pipe']
                });

                let stdout = '';
                let stderr = '';

                process.stdout.on('data', (data) => {
                    stdout += data.toString();
                });

                process.stderr.on('data', (data) => {
                    stderr += data.toString();
                });

                process.on('close', (code) => {
                    if (code === 0) {
                        resolve(this._parseRipgrepOutput(stdout, searchParams));
                    } else if (code === 1) {
                        logger.info('No matches found');
                        resolve([]);
                    } else {
                        logger.warn(`Ripgrep command failed with return code ${code}: ${stderr}`);
                        resolve([]);
                    }
                });

                process.on('error', (err) => {
                    logger.error(`Ripgrep search failed: ${err}`);
                    reject(err);
                });
            });
        } catch (e) {
            logger.error(`Ripgrep search failed: ${e}`);
            return [];
        }
    }

    async search_async(query: string, ...kwargs: any[]): Promise<CodeSnippet[]> {
        return this.search(query, ...kwargs);
    }

    private _parseQuery(query: string): SearchParams {
        const defaultParams: SearchParams = {
            pattern: query,
            caseSensitive: false,
            wholeWord: false,
            regex: false,
            maxContextLines: 5,
            maxResults: 100
        };

        try {
            // Try to parse XML query format if it exists
            if (query.startsWith('<') && query.endsWith('>')) {
                const parser = new DOMParser();
                const doc = parser.parseFromString(query, 'text/xml');
                const queryElement = doc.documentElement;

                if (queryElement.tagName === 'query') {
                    return {
                        pattern: queryElement.textContent || query,
                        caseSensitive: queryElement.getAttribute('case_sensitive') === 'true',
                        wholeWord: queryElement.getAttribute('whole_word') === 'true',
                        regex: queryElement.getAttribute('regex') === 'true',
                        maxContextLines: parseInt(queryElement.getAttribute('max_context_lines') || '5', 10),
                        maxResults: parseInt(queryElement.getAttribute('max_results') || '100', 10)
                    };
                }
            }
        } catch (e) {
            logger.warn(`Failed to parse XML query: ${e}`);
        }

        return defaultParams;
    }

    private _buildRipgrepCommand(params: SearchParams): string[] {
        const cmd = ['rg'];

        // Add options
        if (!params.caseSensitive) cmd.push('-i');
        if (params.wholeWord) cmd.push('-w');
        if (!params.regex) cmd.push('-F');

        // Add context lines
        cmd.push('-C', params.maxContextLines.toString());

        // Add max count
        cmd.push('-m', params.maxResults.toString());

        // Add output format options
        cmd.push('--json');
        cmd.push('--no-config');
        cmd.push('--no-ignore');
        cmd.push('--hidden');

        // Add pattern
        cmd.push(params.pattern);

        // Add search path
        cmd.push(this.repoPath);

        return cmd;
    }

    private _parseRipgrepOutput(output: string, params: SearchParams): CodeSnippet[] {
        const snippets: CodeSnippet[] = [];
        const lines = output.split('\n').filter(line => line.trim());

        for (const line of lines) {
            try {
                const result = JSON.parse(line);
                if (result.type === 'match') {
                    const filePath = result.data.path.text;
                    if (shouldIgnorePath(filePath)) continue;

                    const lineNumber = result.data.line_number;
                    const content = result.data.lines.text;

                    snippets.push({
                        filePath,
                        startLine: lineNumber - 1, // Convert to 0-based
                        endLine: lineNumber - 1,   // Convert to 0-based
                        content,
                        contextBefore: '',
                        contextAfter: '',
                        score: 1.0
                    });
                }
            } catch (e) {
                logger.warn(`Failed to parse ripgrep output line: ${e}`);
                continue;
            }
        }

        return snippets;
    }

    get description(): string {
        return `Grep-based search tool that uses ripgrep for fast and efficient code search.
Features:
- Fast text-based search using ripgrep
- Supports regular expressions
- Case-sensitive/insensitive search
- Whole word matching
- Context line control
- Maximum result limit`;
    }

    get examples(): string {
        return `Example queries:
1. Simple text search:
   "console.log"

2. Case-sensitive search:
   <query case_sensitive="true">Console.log</query>

3. Regular expression:
   <query regex="true">function\\s+\\w+\\s*\\(</query>

4. Whole word match:
   <query whole_word="true">class</query>

5. With context control:
   <query max_context_lines="10">TODO</query>`;
    }
}