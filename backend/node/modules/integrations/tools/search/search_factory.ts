import { createHash } from 'crypto';
import { SearchToolEnum } from '../../../common/constants';
import { SearchToolABC } from './abc_search';
import { AnySearchTool } from './any_search';

const INSTANCE_POOL: { [key: string]: SearchToolABC } = {};

function generateInstanceKey(search_tool: SearchToolEnum, repo_path: string, ...kwargs: any[]): string {
    const params = {
        search_tool: search_tool,
        repo_path: repo_path,
        kwargs: kwargs
    };
    const paramsStr = JSON.stringify(params, Object.keys(params).sort());
    return createHash('sha256').update(paramsStr).digest('hex');
}

export function getSearchToolInstance(search_tool: SearchToolEnum, repo_path: string, ...kwargs: any[]): SearchToolABC {
    const instanceKey = generateInstanceKey(search_tool, repo_path, ...kwargs);

    if (INSTANCE_POOL[instanceKey]) {
        return INSTANCE_POOL[instanceKey];
    }

    const instance = createSearchToolInstance(search_tool, repo_path, ...kwargs);
    INSTANCE_POOL[instanceKey] = instance;
    return instance;
}

function createSearchToolInstance(search_tool: SearchToolEnum, repo_path: string, ...kwargs: any[]): SearchToolABC {
    switch (search_tool) {
        case SearchToolEnum.ANY:
            return new AnySearchTool(repo_path, ...kwargs);
        // Add other search tools here
        default:
            throw new Error(`Unknown search tool: ${search_tool}`);
    }
}