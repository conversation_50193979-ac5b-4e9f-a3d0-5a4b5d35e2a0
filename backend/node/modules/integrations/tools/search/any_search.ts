import { getLogger } from '../../../../utils/trace_logger';
import { SearchToolEnum } from '../../../common/constants';
import { CodeSnippet } from '../../../common/schema';
import { getConfig } from '../../../../core/config';
import { SearchToolABC } from './abc_search';
import { getSearchToolInstance } from './search_factory';

const trace_logger = getLogger(__filename);

export class AnySearchTool extends SearchToolABC {
    private enabled_search_tools: SearchToolEnum[] = [];

    constructor(public repoPath: string, enabled_search_tools: string[] = getConfig().deepsearch.enabled_search_tools) {
        super(repoPath);
        for (const tool of enabled_search_tools) {
            try {
                this.enabled_search_tools.push(tool as SearchToolEnum);
            } catch (e) {
                trace_logger.warn(`Invalid search tool: ${tool}`);
            }
        }
    }

    async search(query: string, search_tool: SearchToolEnum, ...kwargs: any[]): Promise<CodeSnippet[]> {
        if (!this.enabled_search_tools.includes(search_tool)) {
            throw new Error(`Search tool ${search_tool} is not enabled`);
        }

        const searchInstance = getSearchToolInstance(search_tool, this.repoPath);
        return searchInstance.search(query, ...kwargs);
    }

    async search_async(query: string, search_tool: SearchToolEnum, ...kwargs: any[]): Promise<CodeSnippet[]> {
        if (!this.enabled_search_tools.includes(search_tool)) {
            throw new Error(`Search tool ${search_tool} is not enabled`);
        }

        const searchInstance = getSearchToolInstance(search_tool, this.repoPath);
        return searchInstance.search_async(query, ...kwargs);
    }

    get description(): string {
        let descriptions: string[] = [];
        descriptions.push("Unified search interface that provides access to multiple search engines. Choose the most appropriate search tool based on your query type and requirements.\n");

        for (const tool_enum of this.enabled_search_tools) {
            try {
                const search_tool = getSearchToolInstance(tool_enum, this.repoPath);
                descriptions.push(`## Search tool description: ${tool_enum}`);
                descriptions.push(search_tool.description);
                descriptions.push("---");
                descriptions.push("");
            } catch (e) {
                trace_logger.info(`Failed to get description for ${tool_enum}`, e);
                continue;
            }
        }
        return descriptions.join("\n");
    }

    get examples(): string {
        let examples: string[] = [];
        for (const tool_enum of this.enabled_search_tools) {
            try {
                const search_tool = getSearchToolInstance(tool_enum, this.repoPath);
                examples.push(`## Search tool examples: ${tool_enum}`);
                examples.push(search_tool.examples);
                examples.push("---");
                examples.push("");
            } catch (e) {
                trace_logger.info(`Failed to get examples for ${tool_enum}`, e);
                continue;
            }
        }
        return examples.join("\n");
    }
}