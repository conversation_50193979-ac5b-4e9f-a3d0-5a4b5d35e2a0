import { CodeSnippet } from '../../../common/schema';

export abstract class SearchToolABC {
    constructor(public repoPath: string, ...args: any[]) { }

    abstract search(query: string, ...kwargs: any[]): Promise<CodeSnippet[]>;

    async search_async(query: string, ...kwargs: any[]): Promise<CodeSnippet[]> {
        return this.search(query, ...kwargs);
    }

    abstract get description(): string;

    abstract get examples(): string;
}