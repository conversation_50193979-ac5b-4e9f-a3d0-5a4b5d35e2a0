import { LLMClient, defaultLlmClient } from '../llm/llm_client';
import { <PERSON>Result, CodeSnippet, SearchQuery, SearchQueryParser, ContextOperation, ContextOperationResult } from '../common/schema';
import { SearchToolEnum, FileFilterMode } from '../common/constants';
import { getConfig } from '../../core/config';
import { getLogger } from '../../utils/trace_logger';
import { buildFileTree, FileNode, calculateRepoSize } from '../../utils/file';
import { getSearchTool, ISearchTool } from '../deepsearch/search_factory';
import { getSqliteClient } from '../integrations/database/sqlite/client';
import { QUERY_SPLIT_PROMPT, GENERATE_NEW_QUERY_PROMPT, SYSTEM_PROMPTS } from '../deepsearch/prompts';
import { READ_CONTEXT_PROMPT, SNIPPETS_REORDER_PROMPT } from './prompts';
import { AnyIOTool } from '../integrations/tools/io/any_io';

const trace_logger = getLogger(__filename);

export class SearchRouter {
    private repoPath: string;
    private repoId: string;
    private llmClient: LLMClient;
    private searchTool: SearchToolEnum;
    private searchInstance: ISearchTool;
    private config: any;
    private sideMemory: any[] = [];
    private ioInstance: AnyIOTool;

    constructor(repoPath: string, repoId: string = '', llmClient: LLMClient | null = null, searchTool: SearchToolEnum = SearchToolEnum.INVERTED_INDEX) {
        this.repoPath = repoPath;
        this.repoId = repoId;
        this.searchTool = searchTool;
        this.llmClient = llmClient || defaultLlmClient;
        this.config = getConfig().deepsearch;
        this.searchInstance = getSearchTool(repoId, searchTool);
        this.ioInstance = new AnyIOTool(repoPath);
    }

    async init(): Promise<void> {
        await this._prepareRepoData(this.repoPath);
        await this.searchInstance.init();
    }

    async search(query: string): Promise<SearchResult> {
        const result: SearchResult = { 
            originalQuery: query, 
            allQueries: [], 
            codeSnippets: [], 
            iterations: 0 
        };
        
        trace_logger.info(`Start SearchRouter for Query: ${query}`);

        const repoNode: FileNode = {
            id: this.repoPath,
            path: this.repoPath,
            name: this.repoPath.split('/').pop() || '',
            type: 'directory',
            children: buildFileTree(this.repoPath, this.repoPath, 300, FileFilterMode.LOCAL)
        };

        try {
            // 1. 分解查询
            const splitQueries = await this._split_queries(query, repoNode);
            
            // 执行上下文操作
            for (const subQuery of splitQueries) {
                const contextResults = this._executeReadContextOperations(subQuery.context_operations);
                this.sideMemory.push(...contextResults);
            }
            
            let allQueries = [...splitQueries];
            let allCodeSnippets: CodeSnippet[] = [];
            
            // 2. 多轮迭代搜索
            const maxIterations = this.config.max_iterations || 2;
            for (let iteration = 0; iteration < maxIterations; iteration++) {
                if (allQueries.length === 0) {
                    trace_logger.info('未生成新查询，搜索结束');
                    break;
                }
                
                // 生成新查询
                const newQueries = await this._generateNewQueries(query, allQueries);
                allQueries.push(...newQueries);
                
                // 执行搜索
                try {
                    const searchOperations: ContextOperation[] = [];
                    for (const newQuery of newQueries) {
                        searchOperations.push(...newQuery.context_operations);
                    }
                    
                    const newSnippets = await this._searchAndFilter(searchOperations);
                    
                    if (newSnippets.length === 0) {
                        trace_logger.warn(`Iteration ${iteration + 1}: 未找到任何代码片段`);
                        break;
                    }
                    
                    allCodeSnippets.push(...newSnippets);
                    allCodeSnippets = this._mergeSnippets(allCodeSnippets);
                    
                    result.iterations = iteration + 1;
                    
                } catch (e) {
                    trace_logger.error(`Iteration ${iteration + 1}: 搜索失败: ${e}`);
                    break;
                }
            }
            
            // 3. 添加side memory中的文件信息
            for (const element of this.sideMemory) {
                if (element.operation.tool === 'file_io') {
                    allCodeSnippets.push({
                        filePath: element.operation.context_uri,
                        startLine: 0,
                        endLine: Math.max(0, element.result.split('\n').length - 1),
                        content: element.result,
                        contextBefore: '',
                        contextAfter: '',
                        score: 0.0
                    });
                }
            }
            
            result.allQueries = allQueries;
            result.codeSnippets = this._mergeSnippets(allCodeSnippets);
            result.codeSnippets = this._mergeFileSnippets(result.codeSnippets);
            
            // 4. 重排序
            result.codeSnippets = await this._reorderSnippets(query, result.codeSnippets, allQueries);
            
        } catch (e) {
            trace_logger.error(`SearchRouter failed: ${e}`);
            // 如果分解失败，使用原始查询
            try {
                const searchResults = await this.searchInstance.search(query, this.config.max_results || 20);
                const codeSnippets = this._convertSearchResultsToSnippets(searchResults);
                result.allQueries = [{ text: query, context_operations: [] }];
                result.codeSnippets = codeSnippets;
                result.iterations = 1;
            } catch (fallbackError) {
                trace_logger.error(`Fallback search also failed: ${fallbackError}`);
            }
        }

        return result;
    }

    async *searchStream(query: string): AsyncGenerator<string, void, unknown> {
        try {
            yield `data: ${JSON.stringify({"type": "start", "message": "开始搜索", "timestamp": Date.now()})}

`;
            
            const result = await this.search(query);
            
            yield `data: ${JSON.stringify({
                "type": "result", 
                "data": {
                    "query": result.originalQuery,
                    "code_snippets": JSON.stringify(result.codeSnippets.map(snippet => ({
                        file_path: snippet.filePath,
                        content: snippet.content,
                        start_line: snippet.startLine,
                        end_line: snippet.endLine,
                        score: snippet.score || 0
                    })), null, 2)
                },
                "timestamp": Date.now()
            })}

`;
            
            yield `data: ${JSON.stringify({"type": "end", "message": "搜索完成", "timestamp": Date.now()})}

`;
            
        } catch (e) {
            trace_logger.error(`SearchRouter stream failed: ${e}`);
            yield `data: ${JSON.stringify({"type": "error", "message": `搜索失败: ${e}`, "timestamp": Date.now()})}

`;
        }
    }

    private _convertSearchResultsToSnippets(searchResults: any[]): CodeSnippet[] {
        const snippets: CodeSnippet[] = [];
        
        for (const result of searchResults) {
            if (result.chunks && result.chunks.length > 0) {
                for (const chunk of result.chunks) {
                    snippets.push({
                        filePath: result.filePath,
                        content: chunk.content || '',
                        startLine: chunk.startLine || 1,
                        endLine: chunk.endLine || 1,
                        contextBefore: chunk.contextBefore || '',
                        contextAfter: chunk.contextAfter || '',
                        score: result.score || 0
                    });
                }
            } else {
                // Fallback if no chunks
                snippets.push({
                    filePath: result.filePath,
                    content: '',
                    startLine: 1,
                    endLine: 1,
                    contextBefore: '',
                    contextAfter: '',
                    score: result.score || 0
                });
            }
        }
        
        return snippets;
    }

    private async _prepareRepoData(repoPath: string): Promise<void> {
        const sqliteClient = getSqliteClient();
        const repo = await sqliteClient.getRepo(repoPath);
        
        if (repo === null) {
            trace_logger.info(`Repo ${repoPath} not found, creating...`);
            const repoSize = calculateRepoSize(repoPath);
            
            if (repoSize > 50 * 1024 * 1024) { // 50MB
                trace_logger.info(`Repo ${repoPath} size ${repoSize} is too large, skipping...`);
                // 修改配置，只启用grep搜索
                this.config.enabled_search_tools = [SearchToolEnum.GREP];
                return;
            } else {
                trace_logger.info(`Repo ${repoPath} size ${repoSize} is acceptable, preparing...`);
                await sqliteClient.createRepo(repoPath);
            }
        }
    }

    private async _split_queries(query: string, repoNode: FileNode): Promise<SearchQuery[]> {
        try {
            // 构建仓库结构字符串
            const repoStructure = this._buildRepoStructureString(repoNode, 0, 3);
            
            // 构建可用工具描述
            const availableTools = this.ioInstance.availableTools;
            const toolsExamples = this.ioInstance.toolsExamples;
            const toolExamples = `file_io: ${toolsExamples.file_io}\ndirectory_io: ${toolsExamples.directory_io}`;
            
            // 构建prompt
            const prompt = READ_CONTEXT_PROMPT
                .replace('{query}', query)
                .replace('{repo_struct}', repoStructure)
                .replace('{available_tools}', availableTools)
                .replace('{tools_examples}', toolExamples);
            
            trace_logger.info(`Splitting query: ${query}`);
            
            // 调用LLM
            const response = await this.llmClient.completion([
                { role: 'user', content: prompt }
            ]) as string;
            
            // 解析响应
            const queries = SearchQueryParser.parseResponseText(response);
            
            if (queries.length === 0) {
                // 如果解析失败，返回原始查询
                return [{ text: query, context_operations: [] }];
            }
            
            trace_logger.info(`Split into ${queries.length} sub-queries`);
            return queries;
            
        } catch (error) {
            trace_logger.error(`Failed to split query: ${error}`);
            // 出错时返回原始查询
            return [{ text: query, context_operations: [] }];
        }
    }

    private _buildRepoStructureString(node: FileNode, depth: number, maxDepth: number): string {
        if (depth > maxDepth) {
            return '';
        }
        
        const indent = '  '.repeat(depth);
        let result = `${indent}${node.name}${node.type === 'directory' ? '/' : ''}\n`;
        
        if (node.children && node.type === 'directory') {
            for (const child of node.children.slice(0, 10)) { // 限制每层最多10个子项
                result += this._buildRepoStructureString(child, depth + 1, maxDepth);
            }
            if (node.children.length > 10) {
                result += `${indent}  ... (${node.children.length - 10} more items)\n`;
            }
        }
        
        return result;
    }

    private _executeReadContextOperations(contextOperations: ContextOperation[]): ContextOperationResult[] {
        const results: ContextOperationResult[] = [];
        
        for (const operation of contextOperations) {
            try {
                let result = '';
                
                if (operation.tool === 'file_io') {
                    // 使用AnyIOTool读取文件内容
                    result = this.ioInstance.read({ file_io: `<path>${operation.context_uri}</path>` });
                } else if (operation.tool === 'directory_io') {
                    // 使用AnyIOTool读取目录结构
                    result = this.ioInstance.read({ directory_io: `<path>${operation.context_uri}</path>` });
                }
                
                results.push({
                    operation,
                    result
                });
            } catch (error) {
                trace_logger.error(`执行上下文操作失败: ${error}`);
                results.push({
                    operation,
                    result: `操作失败: ${error}`
                });
            }
        }
        
        return results;
    }

    private async _generateNewQueries(originalQuery: string, allQueries: SearchQuery[]): Promise<SearchQuery[]> {
        try {
            // 动态获取搜索工具的描述和示例
            const toolDescription = this.searchInstance.description || '代码搜索工具，用于查找相关代码片段';
            const toolExamples = this.searchInstance.examples || '示例：搜索函数定义、查找类实现等';
            
            const prompt = GENERATE_NEW_QUERY_PROMPT
                .replace('{question}', originalQuery)
                .replace('{context_content}', this.sideMemory.map(element => element.result).join('\n----------\n'))
                .replace('{max_new_queries}', String(this.config.max_new_queries || 2))
                .replace('{tool_description}', toolDescription)
                .replace('{tool_exampls}', toolExamples)
                .replace('{previous_queries}', allQueries.map(q => q.text).join('\n\t- '))
                .replace('{repo_struct}', '')
                .replace('{code_snippets}', '');
            
            const response = await this.llmClient.completion([
                { role: 'user', content: prompt }
            ]) as string;
            
            const queries = SearchQueryParser.parseResponseText(response);
            return queries.slice(0, this.config.max_new_queries || 2);
            
        } catch (error) {
            trace_logger.error(`生成新查询失败: ${error}`);
            return [];
        }
    }

    private async _searchAndFilter(contextOperations: ContextOperation[]): Promise<CodeSnippet[]> {
        trace_logger.info(`异步搜索工具: ${contextOperations.length} 个操作`);
        
        if (contextOperations.length === 0) {
            return [];
        }
        
        const searchResults: CodeSnippet[] = [];
        
        for (const operation of contextOperations) {
            try {
                const results = await this.searchInstance.search(operation.xml_content, this.config.max_results || 20);
                const snippets = this._convertSearchResultsToSnippets(results);
                searchResults.push(...snippets);
                trace_logger.info(`找到 ${snippets.length} 个代码片段`);
            } catch (error) {
                trace_logger.error(`搜索失败: ${error}`);
            }
        }
        
        trace_logger.info(`异步搜索找到 ${searchResults.length} 个代码片段`);
        return searchResults;
    }

    private _mergeSnippets(snippets: CodeSnippet[]): CodeSnippet[] {
        /**
         * 去重代码片段（基于文件路径和行号）
         */
        const seenStack: [string, number, number][] = [];
        const uniqueSnippets: CodeSnippet[] = [];
        
        // 按文件路径、起始行号、结束行号排序
        const sortedSnippets = snippets.sort((a, b) => {
            if (a.filePath !== b.filePath) {
                return a.filePath.localeCompare(b.filePath);
            }
            if (a.startLine !== b.startLine) {
                return a.startLine - b.startLine;
            }
            return a.endLine - b.endLine;
        });
        
        for (const snippet of sortedSnippets) {
            const key: [string, number, number] = [snippet.filePath, snippet.startLine, snippet.endLine];
            
            if (seenStack.length > 0 && 
                seenStack[seenStack.length - 1][0] === key[0] && 
                seenStack[seenStack.length - 1][1] <= key[1] && 
                key[1] <= seenStack[seenStack.length - 1][2]) {
                
                // 更新seenStack的endLine
                seenStack[seenStack.length - 1][2] = Math.max(seenStack[seenStack.length - 1][2], key[2]);
                
                // 如果endLine在前一个snippet后面，则合并snippet内容
                if (uniqueSnippets[uniqueSnippets.length - 1].endLine >= key[2]) {
                    const prevLines = uniqueSnippets[uniqueSnippets.length - 1].content.split('\n');
                    const mergedContent = prevLines.slice(0, snippet.startLine - snippet.startLine).join('\n') + '\n' + snippet.content;
                    uniqueSnippets[uniqueSnippets.length - 1].content = mergedContent;
                    uniqueSnippets[uniqueSnippets.length - 1].score = (uniqueSnippets[uniqueSnippets.length - 1].score || 0) + (snippet.score || 0);
                }
                
                // 更新endLine
                uniqueSnippets[uniqueSnippets.length - 1].endLine = seenStack[seenStack.length - 1][2];
                
            } else {
                seenStack.push([...key]); // 创建副本避免引用问题
                uniqueSnippets.push(snippet);
            }
        }
        
        return uniqueSnippets;
    }

    private _mergeFileSnippets(snippets: CodeSnippet[]): CodeSnippet[] {
        // 合并同一文件中相邻的代码片段
        const fileGroups = new Map<string, CodeSnippet[]>();
        
        for (const snippet of snippets) {
            if (!fileGroups.has(snippet.filePath)) {
                fileGroups.set(snippet.filePath, []);
            }
            fileGroups.get(snippet.filePath)!.push(snippet);
        }
        
        const mergedSnippets: CodeSnippet[] = [];
        
        for (const [filePath, fileSnippets] of Array.from(fileGroups)) {
            // 按行号排序
            fileSnippets.sort((a, b) => a.startLine - b.startLine);
            
            let currentSnippet = fileSnippets[0];
            
            for (let i = 1; i < fileSnippets.length; i++) {
                const nextSnippet = fileSnippets[i];
                
                // 如果相邻或重叠，合并
                if (nextSnippet.startLine <= currentSnippet.endLine + 5) {
                    currentSnippet = {
                        ...currentSnippet,
                        endLine: Math.max(currentSnippet.endLine, nextSnippet.endLine),
                        content: currentSnippet.content + '\n' + nextSnippet.content
                    };
                } else {
                    mergedSnippets.push(currentSnippet);
                    currentSnippet = nextSnippet;
                }
            }
            
            mergedSnippets.push(currentSnippet);
        }
        
        return mergedSnippets;
    }

    private async _reorderSnippets(originalQuery: string, snippets: CodeSnippet[], allQueries: SearchQuery[]): Promise<CodeSnippet[]> {
        if (!snippets || snippets.length === 0) {
            return [];
        }
        
        const combinedQuery = originalQuery + '\n\t- ' + allQueries.map(q => q.text).join('\n\t- ');
        
        // 构建带索引的代码片段摘要
        const codeSummary = this._buildFileIndexSummary(snippets);
        
        const prompt = SNIPPETS_REORDER_PROMPT
            .replace('{max_code_snippets}', '20')
            .replace('{query}', combinedQuery)
            .replace('{code_snippets}', codeSummary);
        
        try {
            const response = await this.llmClient.completion([
                { role: 'system', content: SYSTEM_PROMPTS.reorder },
                { role: 'user', content: prompt }
            ]) as string;
            
            trace_logger.debug(`重排序响应: ${response}`);
            
            // 解析LLM返回的JSON格式结果
            try {
                const scoreDict = JSON.parse(response.trim().replace(/\n/g, ''));
                
                // 为代码片段设置评分
                for (let index = 0; index < snippets.length; index++) {
                    if (scoreDict[String(index)] !== undefined) {
                        snippets[index].score = scoreDict[String(index)];
                    } else {
                        snippets[index].score = 0.0;
                    }
                }
                
                return snippets.sort((a, b) => (b.score || 0) - (a.score || 0)).slice(0, 20);
                
            } catch (parseError) {
                trace_logger.warn('无法从LLM响应中提取JSON格式的评分结果');
                return snippets;
            }
            
        } catch (error) {
            trace_logger.error(`重排序代码片段失败: ${error}`);
            return snippets;
        }
    }
    
    private _buildFileIndexSummary(snippets: CodeSnippet[], snippetChars: number = 500): string {
        if (!snippets || snippets.length === 0) {
            return '暂无相关代码片段';
        }
        
        let summary = '';
        for (let fileIndex = 0; fileIndex < snippets.length; fileIndex++) {
            const snippet = snippets[fileIndex];
            summary += `${fileIndex}. ${snippet.filePath}: ${snippet.startLine}-${snippet.endLine}\n`;
            summary += `  ${snippet.content.substring(0, snippetChars)}\n`;
            summary += `--------------------------------------------\n\n`;
        }
        
        return summary;
    }
}