import { getLogger } from '../../utils/trace_logger';
import { IChunk } from '../chunks/IChunk';
import { getChunkService } from '../chunks/chunk_factory';
import { FileFilterMode } from '../common/constants';
import { getSqliteClient, Repo } from '../integrations/database/sqlite/client';
import { buildFileTree, FileType, FileNode, shouldIgnorePath } from '../../utils/file';
import * as nodejieba from 'nodejieba';
import { Counter } from '../../utils/counter';
import * as fs from 'fs';
import * as path from 'path';

const logger = getLogger('term');

function normalize(word: string): string {
    return word.toLowerCase();
}

const IGNORED_TERMS = new Set<string>();

function shouldIgnore(term: string): boolean {
    // In a real scenario, this would be loaded from a file as in the Python code.
    return IGNORED_TERMS.has(normalize(term));
}

function* _split_code_terms(inputText: string): Generator<string> {
    const pattern = /(?<![a-zA-Z0-9_$])[a-zA-Z_$][a-zA-Z0-9_$]{2,}(?![a-zA-Z0-9_$])/g;
    for (const match of inputText.matchAll(pattern)) {
        const word = match[0];
        if (shouldIgnore(word)) {
            continue;
        }

        const parts = new Set<string>();
        parts.add(normalize(word));

        const sub_parts: string[] = [];

        const camelParts = word.split(/(?<=[a-z0-9])(?=[A-Z])/);
        if (camelParts.length > 1) {
            sub_parts.push(...camelParts);
        }

        const snakeParts = word.split('_');
        if (snakeParts.length > 1) {
            sub_parts.push(...snakeParts.filter(p => p));
            for (const snakePart of snakeParts) {
                if (snakePart) {
                    const camelInSnake = snakePart.split(/(?<=[a-z0-9])(?=[A-Z])/);
                    if (camelInSnake.length > 1) {
                        sub_parts.push(...camelInSnake);
                    }
                }
            }
        }

        const nonDigitPrefixMatch = word.match(/^([^0-9]+)[0-9]+$/);
        if (nonDigitPrefixMatch) {
            const prefix = nonDigitPrefixMatch[1].replace(/_+$/, '');
            if (prefix) {
                sub_parts.push(prefix);
            }
        }

        for (let part of sub_parts) {
            part = part.replace(/^_|_$/g, '');
            if (shouldIgnore(part)) {
                continue;
            }

            if (part.length >= 2 && /[a-zA-Z]/.test(part)) {
                if (part.length === 2 && !/^[a-zA-Z]{2}$/.test(part)) {
                    continue;
                }
                parts.add(normalize(part));
            }
        }

        yield* parts;
    }
}

function* _split_doc_terms(inputText: string): Generator<string> {
    for (const word of nodejieba.cut(inputText)) {
        if (shouldIgnore(word)) {
            continue;
        }
        yield normalize(word);
    }
}

export function getFileType(file_path: string): FileType {
    const doc_extensions = [".md", ".txt", ".json", ".yaml", ".yml"];
    if (doc_extensions.includes(path.extname(file_path).toLowerCase())) {
        return FileType.DOC;
    }
    return FileType.CODE;
}

export function getTerms(text: string, fileType: FileType = FileType.CODE): string[] {
    if (fileType === FileType.CODE) {
        return Array.from(_split_code_terms(text));
    }
    else if (fileType === FileType.DOC) {
        return Array.from(_split_doc_terms(text));
    }
    else {
        throw new Error(`Unknown file type: ${fileType}`);
    }
}


function getAllFilePaths(nodes: FileNode[]): string[] {
    let paths: string[] = [];
    for (const node of nodes) {
        if (node.type === 'file') {
            paths.push(node.path);
        }
        if (node.children) {
            paths = paths.concat(getAllFilePaths(node.children));
        }
    }
    return paths;
}

export function split_code_terms(text: string): Set<string> {
    return new Set(_split_code_terms(text));
}

export function split_doc_terms(text: string): Set<string> {
    return new Set(_split_doc_terms(text));
}

export async function getRepoFiles(repo_path: string): Promise<string[]> {
    const fileNodes = buildFileTree(repo_path, repo_path, 5000, FileFilterMode.EMBEDDING);
    return getAllFilePaths(fileNodes);
}

async function buildRepoTerms(repo_id: string): Promise<void> {
    const sqliteClient = getSqliteClient();
    // 如果 repo_id 是数字字符串，使用 getRepoById，否则使用 getRepo
    const repo = /^\d+$/.test(repo_id) 
        ? await sqliteClient.getRepoById(parseInt(repo_id, 10))
        : await sqliteClient.getRepo(repo_id);

    if (!repo) {
        logger.warn(`Repo ${repo_id} not found`);
        return;
    }

    const files = await getRepoFiles(repo.repo_path);
    logger.info(`Building terms for repo ${repo.repo_path}, ${files.length} files found`);

    const all_terms: string[] = [];
    for (const file_path of files) {
        if (shouldIgnorePath(file_path, FileFilterMode.EMBEDDING)) { // Using embedding filter as in get_repo_files
            continue;
        }
        try {
            const full_path = path.join(repo.repo_path, file_path);
            const content = fs.readFileSync(full_path, 'utf-8');
            const file_type = getFileType(file_path);
            const terms = getTerms(content, file_type);
            all_terms.push(...terms);
        } catch (e) {
            logger.warn(`Failed to read file ${file_path}: ${e}`);
            continue;
        }
    }

    const counter = new Counter(all_terms);
    const counter_result = Array.from(counter.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10000);

    logger.info(`Terms ${counter_result.length} found`);

    const inverted_index: { [key: string]: { count: number, files: string[] } } = {};
    for (const [term, count] of counter_result) {
        inverted_index[term] = {
            count: count,
            files: []
        };
    }

    const chunk_service = getChunkService("line");

    for (const file_path of files) {
        if (shouldIgnorePath(file_path, FileFilterMode.EMBEDDING)) {
            continue;
        }
        try {
            const full_path = path.join(repo.repo_path, file_path);
            const content = fs.readFileSync(full_path, 'utf-8');
            
            const file_type = getFileType(file_path);
            const terms = new Set(getTerms(content, file_type));
            
            const [key_structure, chunks] = chunk_service.chunk_file(file_path, content);
            
            for (const term of terms) {
                if (term in inverted_index) {
                    inverted_index[term].files.push(file_path);
                }
            }
            
            // TODO: update repo structure and chunks in database
            
        } catch (e) {
            logger.warn(`Failed to chunk file ${file_path}: ${e}`);
            continue;
        }
    }
            
    repo.inverted_index = JSON.stringify(inverted_index);
    await sqliteClient.updateRepo(repo, ["inverted_index"]);
    logger.info(`Repo ${repo_id} terms built and saved`);
}