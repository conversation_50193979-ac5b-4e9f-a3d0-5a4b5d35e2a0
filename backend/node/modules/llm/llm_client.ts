import axios, { AxiosError } from 'axios';
import { getLogger } from '../../utils/trace_logger';
import { getConfig, LLMConfig } from '../../core/config';

const logger = getLogger(__filename);

interface LLMChoice {
    delta?: {
        content?: string;
    };
    message?: {
        content: string;
    };
}

interface LLMResponse {
    choices?: LLMChoice[];
}

export class LLMClient {
    private config: LLMConfig;

    constructor() {
        this.config = getConfig().llm;
    }

    private getHeaders(): Record<string, string> {
        return {
            'Authorization': `${this.config.api_key}`,
            'Content-Type': 'application/json'
        };
    }

    private getRequest(messages: { role: string; content: string }[], stream: boolean = false): Record<string, any> {
        return {
            model: this.config.model,
            messages,
            stream,
            temperature: this.config.temperature,
            max_tokens: this.config.max_tokens
        };
    }

    async completion(messages: { role: string; content: string }[], stream: boolean = false): Promise<string | AsyncGenerator<string, void, unknown>> {
        const requestData = this.getRequest(messages, stream);
        const headers = this.getHeaders();

        if (stream) {
            return this.streamCompletion(requestData, headers);
        } else {
            return this.nonStreamCompletion(requestData, headers);
        }
    }

    private async *streamCompletion(requestData: Record<string, any>, headers: Record<string, string>): AsyncGenerator<string, void, unknown> {
        try {
            const response = await axios.post(this.config.base_url, requestData, { headers, responseType: 'stream' });

            if (response.status !== 200) {
                logger.error(`LLM stream call failed with status code: ${response.status}`);
                return;
            }

            for await (const chunk of response.data) {
                const lines = chunk.toString().split('\n');
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const dataContent = line.substring(6);
                        if (dataContent.trim() === '[DONE]') {
                            return;
                        }

                        try {
                            const chunkData: LLMResponse = JSON.parse(dataContent);
                            if (chunkData.choices && chunkData.choices.length > 0) {
                                const choice = chunkData.choices[0];
                                if (choice.delta && choice.delta.content) {
                                    const chunkContent = choice.delta.content;
                                    if (chunkContent) {
                                        yield chunkContent;
                                    }
                                }
                            }
                        } catch (e) {
                            logger.warn(`Failed to parse stream response JSON: ${e}, data: ${dataContent}`);
                        }
                    }
                }
            }
        } catch (error) {
            const axiosError = error as AxiosError;
            logger.error(`LLM stream call failed: ${axiosError.message}`);
        }
    }

    private async nonStreamCompletion(requestData: Record<string, any>, headers: Record<string, string>): Promise<string> {
        try {
            const response = await axios.post(this.config.base_url, requestData, { headers, timeout: this.config.timeout * 1000 });

            if (response.status !== 200) {
                logger.error(`LLM call failed with status code: ${response.status}, response: ${response.data}`);
                return '';
            }

            const result: LLMResponse = response.data;
            if (result.choices && result.choices.length > 0) {
                return result.choices[0].message?.content || '';
            }

            logger.warn(`No choices field in response: ${JSON.stringify(result)}`);
            return '';
        } catch (error) {
            const axiosError = error as AxiosError;
            if (axiosError.code === 'ECONNABORTED') {
                logger.error('LLM call timed out');
            } else {
                logger.error(`LLM call failed: ${axiosError.message}`);
            }
            return '';
        }
    }

    async callAsync(prompt: string, systemPrompt: string = 'You are a professional code analysis assistant', stream: boolean = false): Promise<string> {
        logger.debug(`LLM call prompt: ${prompt}`);

        try {
            const response = await this.completion(
                [
                    { role: 'system', content: systemPrompt },
                    { role: 'user', content: prompt }
                ],
                stream
            );

            if (typeof response === 'string') {
                logger.debug(`LLM response: ${response}`);
                return response;
            } else {
                let responseText = '';
                for await (const chunk of response) {
                    responseText += chunk;
                }
                return responseText;
            }
        } catch (e) {
            const error = e as Error;
            logger.error(`LLM async call failed: ${error.message}`);
            return '';
        }
    }
}

export const defaultLlmClient = new LLMClient();