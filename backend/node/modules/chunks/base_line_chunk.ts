import { IChunk } from './IChunk';
import { Chunk } from '../common/schema';

export class BaseLineChunk implements IChunk {
    chunk_file(filePath: string, fileContent: string, ...args: any[]): [string, Chunk[]] {
        const lines = fileContent.split('\n');
        const chunks: Chunk[] = [];
        
        // Simple line-based chunking - each line is a chunk
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].trim()) { // Skip empty lines
                chunks.push({
                    filePath,
                    startLine: i,
                    endLine: i,
                    content: lines[i]
                });
            }
        }
        
        return [filePath, chunks];
    }
}