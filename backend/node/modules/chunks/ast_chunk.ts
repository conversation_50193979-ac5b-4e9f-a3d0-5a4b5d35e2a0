import { IChunk } from './IChunk';
import { Chunk } from '../common/schema';

export class ASTChunk implements IChunk {
    chunk_file(filePath: string, fileContent: string, ...args: any[]): [string, Chunk[]] {
        const chunks: Chunk[] = [];
        
        // Simple AST-based chunking - split by functions/classes
        // This is a basic implementation that can be enhanced with proper AST parsing
        const lines = fileContent.split('\n');
        let currentChunk: string[] = [];
        let startLine = 0;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            currentChunk.push(line);
            
            // Simple heuristic: end chunk on function/class definitions or empty lines
            if (line.trim().match(/^(function|class|export|const|let|var)\s/) || 
                line.trim() === '' || 
                i === lines.length - 1) {
                
                if (currentChunk.length > 0 && currentChunk.some(l => l.trim())) {
                    chunks.push({
                        filePath,
                        startLine,
                        endLine: i,
                        content: currentChunk.join('\n')
                    });
                }
                
                currentChunk = [];
                startLine = i + 1;
            }
        }
        
        return [filePath, chunks];
    }
}