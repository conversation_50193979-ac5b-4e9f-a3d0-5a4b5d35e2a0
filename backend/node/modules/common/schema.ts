import { safeExtractXmlTagsWithPreprocessing, safeParseXmlWithPreprocessing, extractTextFromXmlElement } from '../../utils/xml_utils';
import { SearchToolEnum, IOToolEnum } from './constants';
import { XMLSerializer } from '@xmldom/xmldom';

export interface Chunk {
    filePath: string;
    startLine: number; // 0-based
    endLine: number;   // 0-based
    content: string;
}

export interface CodeSnippet extends Chunk {
    contextBefore: string;
    contextAfter: string;
    score: number;
}

export interface ContextOperation {
    tool: string;
    xml_content: string;
    context_uri: string;
}

export interface ContextOperationResult {
    operation: ContextOperation;
    result: string;
}

export interface SearchQuery {
    text: string;
    context_operations: ContextOperation[];
}

export class SearchQueryParser {
    static parseResponseText(responseText: string): SearchQuery[] {
        const searchQueries: SearchQuery[] = [];

        // 处理sub_query标签
        const extractedTags = safeExtractXmlTagsWithPreprocessing(responseText, ['sub_query']);
        
        for (const [, tagContent] of extractedTags) {
            try {
                // 解析单个sub_query标签
                const doc = safeParseXmlWithPreprocessing(tagContent);
                if (!doc) {
                    continue;
                }

                const root = doc.documentElement;
                if (!root) {
                    continue;
                }

                // 提取text内容
                const textElements = root.getElementsByTagName('text');
                if (textElements.length === 0) {
                    continue;
                }

                const textContent = extractTextFromXmlElement(textElements[0]);
                if (!textContent) {
                    continue;
                }

                // 提取context操作
                const contextOperations: ContextOperation[] = [];
                const contextElements = root.getElementsByTagName('context');
                
                if (contextElements.length > 0) {
                    const contextElement = contextElements[0];
                    
                    // 查找所有IO工具操作
                    for (const ioTool of Object.values(IOToolEnum)) {
                        const ioElements = contextElement.getElementsByTagName(ioTool);
                        for (let i = 0; i < ioElements.length; i++) {
                            const ioElement = ioElements[i];
                            const pathElements = ioElement.getElementsByTagName('path');
                            
                            if (pathElements.length > 0) {
                                const pathContent = extractTextFromXmlElement(pathElements[0]);
                                if (pathContent) {
                                    // 将XML元素转换为字符串
                                    const serializer = new XMLSerializer();
                                    const xmlContent = serializer.serializeToString(ioElement);
                                    contextOperations.push({
                                        tool: ioTool,
                                        xml_content: xmlContent,
                                        context_uri: pathContent
                                    });
                                }
                            }
                        }
                    }
                }

                // 创建SearchQuery对象
                const searchQuery: SearchQuery = {
                    text: textContent,
                    context_operations: contextOperations
                };
                searchQueries.push(searchQuery);

            } catch (error) {
                // 如果解析失败，跳过并继续处理下一个
                continue;
            }
        }

        // 处理搜索工具标签
        const searchToolTags = Object.values(SearchToolEnum);
        const structureTags = safeExtractXmlTagsWithPreprocessing(responseText, searchToolTags);

        for (const [tagName, tagContent] of structureTags) {
            try {
                // 根据标签名找到对应的工具枚举
                let toolEnum: SearchToolEnum | null = null;
                for (const tool of Object.values(SearchToolEnum)) {
                    if (tool === tagName) {
                        toolEnum = tool;
                        break;
                    }
                }

                if (!toolEnum) {
                    continue;
                }

                // 为每个搜索工具查询创建一个SearchQuery对象
                const contextOperation: ContextOperation = {
                    tool: toolEnum,
                    xml_content: tagContent,
                    context_uri: ""
                };

                // 从XML内容中提取文本作为查询文本
                let textContent = tagContent;
                try {
                    const doc = safeParseXmlWithPreprocessing(tagContent);
                    if (doc && doc.documentElement) {
                        const root = doc.documentElement;
                        
                        // 首先尝试提取query子标签
                        const queryElements = root.getElementsByTagName('query');
                        if (queryElements.length > 0) {
                            const queryText = extractTextFromXmlElement(queryElements[0]);
                            if (queryText) {
                                textContent = queryText;
                            }
                        } else {
                            // 如果没有query子标签，使用根元素的文本内容
                            const rootText = extractTextFromXmlElement(root);
                            if (rootText) {
                                textContent = rootText;
                            }
                        }
                    }
                } catch {
                    // 如果解析失败，使用原始内容
                }

                const searchQuery: SearchQuery = {
                    text: textContent,
                    context_operations: [contextOperation]
                };
                searchQueries.push(searchQuery);

            } catch (error) {
                // 如果解析失败，跳过并继续处理下一个
                continue;
            }
        }

        return searchQueries;
    }
}

export interface SearchResult {
    originalQuery: string;
    allQueries: SearchQuery[];
    codeSnippets: CodeSnippet[];
    iterations: number;
}