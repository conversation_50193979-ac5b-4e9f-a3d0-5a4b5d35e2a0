export enum LanguageEnum {
    JAVA = 'java',
    PYTHON = 'python',
    C = 'c',
    CPP = 'cpp',
    GO = 'go',
    RUST = 'rust',
    PHP = 'php',
    RUBY = 'ruby',
    SWIFT = 'swift',
    KOTLIN = 'kotlin',
    SCALA = 'scala',
    HTML = 'html',
    CSS = 'css',
    JAVASCRIPT = 'javascript',
    TYPESCRIPT = 'typescript',
    JSX = 'jsx',
    TSX = 'tsx',
    JSON = 'json',
    XML = 'xml',
    YAML = 'yaml',
    YML = 'yml',
    TOML = 'toml',
    MD = 'markdown',
    SQL = 'sql',
    SH = 'sh',
    BASH = 'bash',
    ZSH = 'zsh',
    FISH = 'fish',
    DOCKERFILE = 'dockerfile',
    MAKEFILE = 'makefile'
}

export function languageEnumFromSuffix(suffix: string): LanguageEnum | undefined {
    const lowerSuffix = suffix.toLowerCase();
    for (const lang of Object.values(LanguageEnum)) {
        if (lang === lowerSuffix) {
            return lang;
        }
    }
    return undefined;
}

export enum FileFilterMode {
    LOCAL = 'local',
    EMBEDDING = 'embedding'
}

export enum SearchToolEnum {
    GREP = 'grep',
    EMBEDDING = 'embedding',
    INVERTED_INDEX = 'inverted_index',
    TERM_SPRSE = 'term_sparse',
    ANY = 'any'
}

export enum IOToolEnum {
    FILE = 'file_io',
    DIRECTORY = 'directory_io'
}