{"name": "node-backend", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "node-backend", "version": "1.0.0", "license": "ISC", "dependencies": {"@xmldom/xmldom": "^0.8.11", "axios": "^1.11.0", "express": "^5.1.0", "js-yaml": "^4.1.0", "nodejieba": "^3.4.4", "sequelize": "^6.37.7", "sqlite3": "^5.1.7", "tree-sitter": "^0.21.1", "tree-sitter-javascript": "^0.23.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "yargs": "^18.0.0"}, "devDependencies": {"@types/express": "^5.0.3", "@types/js-yaml": "^4.0.9", "@types/minimatch": "^5.1.2", "@types/node": "^20.12.12", "@types/xmldom": "^0.1.34", "@types/yargs": "^17.0.33", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}, "node_modules/@colors/colors": {"version": "1.6.0", "resolved": "http://registry.m.jd.com/@colors/colors/download/@colors/colors-1.6.0.tgz", "integrity": "sha1-7GzSN0QHALwjyiMIf1E8dVCJWLA=", "license": "MIT", "engines": {"node": ">=0.1.90"}}, "node_modules/@cspotcode/source-map-support": {"version": "0.8.1", "resolved": "http://registry.m.jd.com/@cspotcode/source-map-support/download/@cspotcode/source-map-support-0.8.1.tgz", "integrity": "sha1-AGKcNaaI4FqIsc2mhPudXnPwAKE=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "0.3.9"}, "engines": {"node": ">=12"}}, "node_modules/@dabh/diagnostics": {"version": "2.0.3", "resolved": "http://registry.m.jd.com/@dabh/diagnostics/download/@dabh/diagnostics-2.0.3.tgz", "integrity": "sha1-f36X7ppyXf/HgI2TZozJhOHcR3o=", "license": "MIT", "dependencies": {"colorspace": "1.1.x", "enabled": "2.0.x", "kuler": "^2.0.0"}}, "node_modules/@gar/promisify": {"version": "1.1.3", "resolved": "http://registry.m.jd.com/@gar/promisify/download/@gar/promisify-1.1.3.tgz", "integrity": "sha1-VVGTqy47s7atw9VRycAw2ehg2vY=", "license": "MIT", "optional": true}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "http://registry.m.jd.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz", "integrity": "sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.5", "resolved": "http://registry.m.jd.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.5.tgz", "integrity": "sha1-aRKwDSxjHA0Vzhp6tXzWV/Ko+Lo=", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.9", "resolved": "http://registry.m.jd.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.9.tgz", "integrity": "sha1-ZTT9WTOlO6fL86F2FeJzoNEnP/k=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "node_modules/@mapbox/node-pre-gyp": {"version": "1.0.11", "resolved": "http://registry.m.jd.com/@mapbox/node-pre-gyp/download/@mapbox/node-pre-gyp-1.0.11.tgz", "integrity": "sha1-QX20K39TI9eek7NKbXoqEsDfQ/o=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"detect-libc": "^2.0.0", "https-proxy-agent": "^5.0.0", "make-dir": "^3.1.0", "node-fetch": "^2.6.7", "nopt": "^5.0.0", "npmlog": "^5.0.1", "rimraf": "^3.0.2", "semver": "^7.3.5", "tar": "^6.1.11"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "http://registry.m.jd.com/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/are-we-there-yet": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/are-we-there-yet/download/are-we-there-yet-2.0.0.tgz", "integrity": "sha1-Ny4Oe9J52OlMZTqqH2cgCIS/Phw=", "license": "ISC", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": ">=10"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/emoji-regex": {"version": "8.0.0", "resolved": "http://registry.m.jd.com/emoji-regex/download/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "license": "MIT"}, "node_modules/@mapbox/node-pre-gyp/node_modules/gauge": {"version": "3.0.2", "resolved": "http://registry.m.jd.com/gauge/download/gauge-3.0.2.tgz", "integrity": "sha1-A79EQcBEODkIvPoGVq2RgDJZs5U=", "deprecated": "This package is no longer supported.", "license": "ISC", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "color-support": "^1.1.2", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.1", "object-assign": "^4.1.1", "signal-exit": "^3.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "wide-align": "^1.1.2"}, "engines": {"node": ">=10"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/npmlog": {"version": "5.0.1", "resolved": "http://registry.m.jd.com/npmlog/download/npmlog-5.0.1.tgz", "integrity": "sha1-8GZ46A4pQZrWerlk4PpplZweuLA=", "deprecated": "This package is no longer supported.", "license": "ISC", "dependencies": {"are-we-there-yet": "^2.0.0", "console-control-strings": "^1.1.0", "gauge": "^3.0.0", "set-blocking": "^2.0.0"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/string-width": {"version": "4.2.3", "resolved": "http://registry.m.jd.com/string-width/download/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/@mapbox/node-pre-gyp/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "http://registry.m.jd.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/@npmcli/fs": {"version": "1.1.1", "resolved": "http://registry.m.jd.com/@npmcli/fs/download/@npmcli/fs-1.1.1.tgz", "integrity": "sha1-cvcZ/pNeaHxWpPrs88A9BrpZMlc=", "license": "ISC", "optional": true, "dependencies": {"@gar/promisify": "^1.0.1", "semver": "^7.3.5"}}, "node_modules/@npmcli/move-file": {"version": "1.1.2", "resolved": "http://registry.m.jd.com/@npmcli/move-file/download/@npmcli/move-file-1.1.2.tgz", "integrity": "sha1-GoLD43L3yuklPrZtclQ9a4aFxnQ=", "deprecated": "This functionality has been moved to @npmcli/fs", "license": "MIT", "optional": true, "dependencies": {"mkdirp": "^1.0.4", "rimraf": "^3.0.2"}, "engines": {"node": ">=10"}}, "node_modules/@tootallnate/once": {"version": "1.1.2", "resolved": "http://registry.m.jd.com/@tootallnate/once/download/@tootallnate/once-1.1.2.tgz", "integrity": "sha1-zLkURTYBeaBOf+av94wA/8Hur4I=", "license": "MIT", "optional": true, "engines": {"node": ">= 6"}}, "node_modules/@tsconfig/node10": {"version": "1.0.11", "resolved": "http://registry.m.jd.com/@tsconfig/node10/download/@tsconfig/node10-1.0.11.tgz", "integrity": "sha1-buRkAGhfEw4ngSjHs4t+Ax/1svI=", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node12": {"version": "1.0.11", "resolved": "http://registry.m.jd.com/@tsconfig/node12/download/@tsconfig/node12-1.0.11.tgz", "integrity": "sha1-7j3vHyfZ7WbaxuRqKVz/sBUuBY0=", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node14": {"version": "1.0.3", "resolved": "http://registry.m.jd.com/@tsconfig/node14/download/@tsconfig/node14-1.0.3.tgz", "integrity": "sha1-5DhjFihPALmENb9A9y91oJ2r9sE=", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node16": {"version": "1.0.4", "resolved": "http://registry.m.jd.com/@tsconfig/node16/download/@tsconfig/node16-1.0.4.tgz", "integrity": "sha1-C5LcwMwcgfbzBqOB8o4xsaVlNuk=", "dev": true, "license": "MIT"}, "node_modules/@types/body-parser": {"version": "1.19.6", "resolved": "http://registry.m.jd.com/@types/body-parser/download/@types/body-parser-1.19.6.tgz", "integrity": "sha1-GFm+u4/X2smRikXVTBlxq4ta9HQ=", "dev": true, "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/connect": {"version": "3.4.38", "resolved": "http://registry.m.jd.com/@types/connect/download/@types/connect-3.4.38.tgz", "integrity": "sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/debug": {"version": "4.1.12", "resolved": "http://registry.m.jd.com/@types/debug/download/@types/debug-4.1.12.tgz", "integrity": "sha1-oVXyFpCHGVNBDfS2tvUxh/BQCRc=", "license": "MIT", "dependencies": {"@types/ms": "*"}}, "node_modules/@types/express": {"version": "5.0.3", "resolved": "http://registry.m.jd.com/@types/express/download/@types/express-5.0.3.tgz", "integrity": "sha1-bEvGrN3C4qWHFC4di+C84gdX6VY=", "dev": true, "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^5.0.0", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "5.0.7", "resolved": "http://registry.m.jd.com/@types/express-serve-static-core/download/@types/express-serve-static-core-5.0.7.tgz", "integrity": "sha1-L6lIecnUaxGl30x0rHW+/WsoPeY=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/http-errors": {"version": "2.0.5", "resolved": "http://registry.m.jd.com/@types/http-errors/download/@types/http-errors-2.0.5.tgz", "integrity": "sha1-W3SasrFroRNCP+saZKldzTA5hHI=", "dev": true, "license": "MIT"}, "node_modules/@types/js-yaml": {"version": "4.0.9", "resolved": "http://registry.m.jd.com/@types/js-yaml/download/@types/js-yaml-4.0.9.tgz", "integrity": "sha1-zYI4LE+QL+2WkaLteexoxYmK9MI=", "dev": true, "license": "MIT"}, "node_modules/@types/mime": {"version": "1.3.5", "resolved": "http://registry.m.jd.com/@types/mime/download/@types/mime-1.3.5.tgz", "integrity": "sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=", "dev": true, "license": "MIT"}, "node_modules/@types/minimatch": {"version": "5.1.2", "resolved": "http://registry.m.jd.com/@types/minimatch/download/@types/minimatch-5.1.2.tgz", "integrity": "sha1-B1CLRXl8uB7D8nMBGwVM0HVe3co=", "dev": true, "license": "MIT"}, "node_modules/@types/ms": {"version": "2.1.0", "resolved": "http://registry.m.jd.com/@types/ms/download/@types/ms-2.1.0.tgz", "integrity": "sha1-BSqmekjszEMJ1/AZG35BQ0uQu3g=", "license": "MIT"}, "node_modules/@types/node": {"version": "20.19.11", "resolved": "http://registry.m.jd.com/@types/node/download/@types/node-20.19.11.tgz", "integrity": "sha1-coyrUwkr1fFDvu1/u6e6md48FsQ=", "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/qs": {"version": "6.14.0", "resolved": "http://registry.m.jd.com/@types/qs/download/@types/qs-6.14.0.tgz", "integrity": "sha1-2LYM7PYvLbD7aOXgBgd7kXi4XeU=", "dev": true, "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.7", "resolved": "http://registry.m.jd.com/@types/range-parser/download/@types/range-parser-1.2.7.tgz", "integrity": "sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=", "dev": true, "license": "MIT"}, "node_modules/@types/send": {"version": "0.17.5", "resolved": "http://registry.m.jd.com/@types/send/download/@types/send-0.17.5.tgz", "integrity": "sha1-2ZHU8rFvKx70lxMfAKkRQpB5HnQ=", "dev": true, "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.8", "resolved": "http://registry.m.jd.com/@types/serve-static/download/@types/serve-static-1.15.8.tgz", "integrity": "sha1-gYDD++SnDo8AufcLm6fwjzWYeHc=", "dev": true, "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/@types/triple-beam": {"version": "1.3.5", "resolved": "http://registry.m.jd.com/@types/triple-beam/download/@types/triple-beam-1.3.5.tgz", "integrity": "sha1-dP75/7qhmOuLWIvgKfOLACmcqiw=", "license": "MIT"}, "node_modules/@types/validator": {"version": "13.15.2", "resolved": "http://registry.m.jd.com/@types/validator/download/@types/validator-13.15.2.tgz", "integrity": "sha1-7lM6IKuXffNpF6RUdUx+DfSqb48=", "license": "MIT"}, "node_modules/@types/xmldom": {"version": "0.1.34", "resolved": "http://registry.m.jd.com/@types/xmldom/download/@types/xmldom-0.1.34.tgz", "integrity": "sha1-p1L3O98JzG14s9Oy58pN0EzJb9I=", "dev": true, "license": "MIT"}, "node_modules/@types/yargs": {"version": "17.0.33", "resolved": "http://registry.m.jd.com/@types/yargs/download/@types/yargs-17.0.33.tgz", "integrity": "sha1-jDIwPag+7AUKhLPHrnufki0T4y0=", "dev": true, "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@types/yargs-parser": {"version": "21.0.3", "resolved": "http://registry.m.jd.com/@types/yargs-parser/download/@types/yargs-parser-21.0.3.tgz", "integrity": "sha1-gV4wt4bS6PDc2F/VvPXhoE0AjxU=", "dev": true, "license": "MIT"}, "node_modules/@xmldom/xmldom": {"version": "0.8.11", "resolved": "http://registry.m.jd.com/@xmldom/xmldom/download/@xmldom/xmldom-0.8.11.tgz", "integrity": "sha1-t53i1nOJc0xXxSWV96cwXjDC1gg=", "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/abbrev": {"version": "1.1.1", "resolved": "http://registry.m.jd.com/abbrev/download/abbrev-1.1.1.tgz", "integrity": "sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=", "license": "ISC"}, "node_modules/accepts": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/accepts/download/accepts-2.0.0.tgz", "integrity": "sha1-u89LpQdUZ/PyEx6rPP/HPC9deJU=", "license": "MIT", "dependencies": {"mime-types": "^3.0.0", "negotiator": "^1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.15.0", "resolved": "http://registry.m.jd.com/acorn/download/acorn-8.15.0.tgz", "integrity": "sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-walk": {"version": "8.3.4", "resolved": "http://registry.m.jd.com/acorn-walk/download/acorn-walk-8.3.4.tgz", "integrity": "sha1-eU3RacOXft9LpOpHWDWHxYZiNrc=", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.11.0"}, "engines": {"node": ">=0.4.0"}}, "node_modules/agent-base": {"version": "6.0.2", "resolved": "http://registry.m.jd.com/agent-base/download/agent-base-6.0.2.tgz", "integrity": "sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=", "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/agentkeepalive": {"version": "4.6.0", "resolved": "http://registry.m.jd.com/agentkeepalive/download/agentkeepalive-4.6.0.tgz", "integrity": "sha1-Nfc+lLP0C/ZfEFIZxiOtGcE26mo=", "license": "MIT", "optional": true, "dependencies": {"humanize-ms": "^1.2.1"}, "engines": {"node": ">= 8.0.0"}}, "node_modules/aggregate-error": {"version": "3.1.0", "resolved": "http://registry.m.jd.com/aggregate-error/download/aggregate-error-3.1.0.tgz", "integrity": "sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=", "license": "MIT", "optional": true, "dependencies": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/ansi-regex": {"version": "6.2.0", "resolved": "http://registry.m.jd.com/ansi-regex/download/ansi-regex-6.2.0.tgz", "integrity": "sha1-LzAudVBDGxt3YnBf/7Us8f+iBEc=", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/ansi-styles": {"version": "6.2.1", "resolved": "http://registry.m.jd.com/ansi-styles/download/ansi-styles-6.2.1.tgz", "integrity": "sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/anymatch": {"version": "3.1.3", "resolved": "http://registry.m.jd.com/anymatch/download/anymatch-3.1.3.tgz", "integrity": "sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=", "dev": true, "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/aproba": {"version": "2.1.0", "resolved": "http://registry.m.jd.com/aproba/download/aproba-2.1.0.tgz", "integrity": "sha1-dVAKGQMT2Vxk6HHn5ChMasIZ8LE=", "license": "ISC"}, "node_modules/are-we-there-yet": {"version": "3.0.1", "resolved": "http://registry.m.jd.com/are-we-there-yet/download/are-we-there-yet-3.0.1.tgz", "integrity": "sha1-Z53yIrJ4xk8s26EXXNwAsNlhZL0=", "license": "ISC", "optional": true, "dependencies": {"delegates": "^1.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/arg": {"version": "4.1.3", "resolved": "http://registry.m.jd.com/arg/download/arg-4.1.3.tgz", "integrity": "sha1-Jp/HrVuOQstjyJbVZmAXJhwUQIk=", "dev": true, "license": "MIT"}, "node_modules/argparse": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/argparse/download/argparse-2.0.1.tgz", "integrity": "sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=", "license": "Python-2.0"}, "node_modules/async": {"version": "3.2.6", "resolved": "http://registry.m.jd.com/async/download/async-3.2.6.tgz", "integrity": "sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=", "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "http://registry.m.jd.com/asynckit/download/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "license": "MIT"}, "node_modules/axios": {"version": "1.11.0", "resolved": "http://registry.m.jd.com/axios/download/axios-1.11.0.tgz", "integrity": "sha1-wuwhnjXkFMAlsglei4KAJ4R4/bY=", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.4", "proxy-from-env": "^1.1.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/balanced-match/download/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "license": "MIT"}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "http://registry.m.jd.com/base64-js/download/base64-js-1.5.1.tgz", "integrity": "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/binary-extensions": {"version": "2.3.0", "resolved": "http://registry.m.jd.com/binary-extensions/download/binary-extensions-2.3.0.tgz", "integrity": "sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/bindings": {"version": "1.5.0", "resolved": "http://registry.m.jd.com/bindings/download/bindings-1.5.0.tgz", "integrity": "sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=", "license": "MIT", "dependencies": {"file-uri-to-path": "1.0.0"}}, "node_modules/bl": {"version": "4.1.0", "resolved": "http://registry.m.jd.com/bl/download/bl-4.1.0.tgz", "integrity": "sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=", "license": "MIT", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/body-parser": {"version": "2.2.0", "resolved": "http://registry.m.jd.com/body-parser/download/body-parser-2.2.0.tgz", "integrity": "sha1-96llbeMFJJpxW1Sbe4/Rq5393Po=", "license": "MIT", "dependencies": {"bytes": "^3.1.2", "content-type": "^1.0.5", "debug": "^4.4.0", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "on-finished": "^2.4.1", "qs": "^6.14.0", "raw-body": "^3.0.0", "type-is": "^2.0.0"}, "engines": {"node": ">=18"}}, "node_modules/brace-expansion": {"version": "1.1.12", "resolved": "http://registry.m.jd.com/brace-expansion/download/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "http://registry.m.jd.com/braces/download/braces-3.0.3.tgz", "integrity": "sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/buffer": {"version": "5.7.1", "resolved": "http://registry.m.jd.com/buffer/download/buffer-5.7.1.tgz", "integrity": "sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/bytes": {"version": "3.1.2", "resolved": "http://registry.m.jd.com/bytes/download/bytes-3.1.2.tgz", "integrity": "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/cacache": {"version": "15.3.0", "resolved": "http://registry.m.jd.com/cacache/download/cacache-15.3.0.tgz", "integrity": "sha1-3IU4D7L1Vv492kxxm/oOyHWn8es=", "license": "ISC", "optional": true, "dependencies": {"@npmcli/fs": "^1.0.0", "@npmcli/move-file": "^1.0.1", "chownr": "^2.0.0", "fs-minipass": "^2.0.0", "glob": "^7.1.4", "infer-owner": "^1.0.4", "lru-cache": "^6.0.0", "minipass": "^3.1.1", "minipass-collect": "^1.0.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.2", "mkdirp": "^1.0.3", "p-map": "^4.0.0", "promise-inflight": "^1.0.1", "rimraf": "^3.0.2", "ssri": "^8.0.1", "tar": "^6.0.2", "unique-filename": "^1.1.1"}, "engines": {"node": ">= 10"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha1-S1QowiK+mF15w9gmV0edvgtZstY=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "http://registry.m.jd.com/call-bound/download/call-bound-1.0.4.tgz", "integrity": "sha1-I43pNdKippKSjFOMfM+pEGf9Bio=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/chokidar": {"version": "3.6.0", "resolved": "http://registry.m.jd.com/chokidar/download/chokidar-3.6.0.tgz", "integrity": "sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=", "dev": true, "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chownr": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/chownr/download/chownr-2.0.0.tgz", "integrity": "sha1-Fb++U9LqtM9w8YqM1o6+Wzyx3s4=", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/clean-stack": {"version": "2.2.0", "resolved": "http://registry.m.jd.com/clean-stack/download/clean-stack-2.2.0.tgz", "integrity": "sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=", "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/cliui": {"version": "9.0.1", "resolved": "http://registry.m.jd.com/cliui/download/cliui-9.0.1.tgz", "integrity": "sha1-b3iQ84b28feZU63B943sRvzC0pE=", "license": "ISC", "dependencies": {"string-width": "^7.2.0", "strip-ansi": "^7.1.0", "wrap-ansi": "^9.0.0"}, "engines": {"node": ">=20"}}, "node_modules/color": {"version": "3.2.1", "resolved": "http://registry.m.jd.com/color/download/color-3.2.1.tgz", "integrity": "sha1-NUTcGYyvRJDD7MmnkLVP6f9F4WQ=", "license": "MIT", "dependencies": {"color-convert": "^1.9.3", "color-string": "^1.6.0"}}, "node_modules/color-convert": {"version": "1.9.3", "resolved": "http://registry.m.jd.com/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/color-name": {"version": "1.1.3", "resolved": "http://registry.m.jd.com/color-name/download/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "license": "MIT"}, "node_modules/color-string": {"version": "1.9.1", "resolved": "http://registry.m.jd.com/color-string/download/color-string-1.9.1.tgz", "integrity": "sha1-RGf5FG8Db4Vbdk37W/hYK/NCx6Q=", "license": "MIT", "dependencies": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "node_modules/color-support": {"version": "1.1.3", "resolved": "http://registry.m.jd.com/color-support/download/color-support-1.1.3.tgz", "integrity": "sha1-k4NDeaHMmgxh+C9S8NBDIiUb1aI=", "license": "ISC", "bin": {"color-support": "bin.js"}}, "node_modules/colorspace": {"version": "1.1.4", "resolved": "http://registry.m.jd.com/colorspace/download/colorspace-1.1.4.tgz", "integrity": "sha1-jUQtEYYVL2BFO/gHDNZus2TlkkM=", "license": "MIT", "dependencies": {"color": "^3.1.3", "text-hex": "1.0.x"}}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "http://registry.m.jd.com/combined-stream/download/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "http://registry.m.jd.com/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "license": "MIT"}, "node_modules/console-control-strings": {"version": "1.1.0", "resolved": "http://registry.m.jd.com/console-control-strings/download/console-control-strings-1.1.0.tgz", "integrity": "sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=", "license": "ISC"}, "node_modules/content-disposition": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/content-disposition/download/content-disposition-1.0.0.tgz", "integrity": "sha1-hEQmyzmPk0yu/LsXIgASa8fOrOI=", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.5", "resolved": "http://registry.m.jd.com/content-type/download/content-type-1.0.5.tgz", "integrity": "sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "0.7.2", "resolved": "http://registry.m.jd.com/cookie/download/cookie-0.7.2.tgz", "integrity": "sha1-VWNpxHKiupEPKXmJG1JrNDYjftc=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.2.2", "resolved": "http://registry.m.jd.com/cookie-signature/download/cookie-signature-1.2.2.tgz", "integrity": "sha1-V8f8PMKTrKuf7FTXPhVpDr5KF5M=", "license": "MIT", "engines": {"node": ">=6.6.0"}}, "node_modules/create-require": {"version": "1.1.1", "resolved": "http://registry.m.jd.com/create-require/download/create-require-1.1.1.tgz", "integrity": "sha1-wdfo8eX2z8n/ZfnNNS03NIdWwzM=", "dev": true, "license": "MIT"}, "node_modules/debug": {"version": "4.4.1", "resolved": "http://registry.m.jd.com/debug/download/debug-4.4.1.tgz", "integrity": "sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decompress-response": {"version": "6.0.0", "resolved": "http://registry.m.jd.com/decompress-response/download/decompress-response-6.0.0.tgz", "integrity": "sha1-yjh2Et234QS9FthaqwDV7PCcZvw=", "license": "MIT", "dependencies": {"mimic-response": "^3.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/deep-extend": {"version": "0.6.0", "resolved": "http://registry.m.jd.com/deep-extend/download/deep-extend-0.6.0.tgz", "integrity": "sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw=", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/delayed-stream/download/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/delegates": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/delegates/download/delegates-1.0.0.tgz", "integrity": "sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=", "license": "MIT"}, "node_modules/depd": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/depd/download/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/detect-libc": {"version": "2.0.4", "resolved": "http://registry.m.jd.com/detect-libc/download/detect-libc-2.0.4.tgz", "integrity": "sha1-8EcVuLqBXlO02BCWVbZQimhlp+g=", "license": "Apache-2.0", "engines": {"node": ">=8"}}, "node_modules/diff": {"version": "4.0.2", "resolved": "http://registry.m.jd.com/diff/download/diff-4.0.2.tgz", "integrity": "sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/dottie": {"version": "2.0.6", "resolved": "http://registry.m.jd.com/dottie/download/dottie-2.0.6.tgz", "integrity": "sha1-NFZOv8bsXldyJy1GZCStW2lkhNQ=", "license": "MIT"}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/dunder-proto/download/dunder-proto-1.0.1.tgz", "integrity": "sha1-165mfh3INIL4tw/Q9u78UNow9Yo=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "http://registry.m.jd.com/ee-first/download/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "license": "MIT"}, "node_modules/emoji-regex": {"version": "10.4.0", "resolved": "http://registry.m.jd.com/emoji-regex/download/emoji-regex-10.4.0.tgz", "integrity": "sha1-A1U6/qgLOXV0nPyzb3dsomjkE9Q=", "license": "MIT"}, "node_modules/enabled": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/enabled/download/enabled-2.0.0.tgz", "integrity": "sha1-+d2S7C1vS7wNXR5k4h1hzUZl58I=", "license": "MIT"}, "node_modules/encodeurl": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/encodeurl/download/encodeurl-2.0.0.tgz", "integrity": "sha1-e46omAd9fkCdOsRUdOo46vCFelg=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/encoding": {"version": "0.1.13", "resolved": "http://registry.m.jd.com/encoding/download/encoding-0.1.13.tgz", "integrity": "sha1-VldK/deR9UqOmyeFwFgqLSYhD6k=", "license": "MIT", "optional": true, "dependencies": {"iconv-lite": "^0.6.2"}}, "node_modules/end-of-stream": {"version": "1.4.5", "resolved": "http://registry.m.jd.com/end-of-stream/download/end-of-stream-1.4.5.tgz", "integrity": "sha1-c0TXEd6kDgt0q8LtSXeHQ8ztsIw=", "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/env-paths": {"version": "2.2.1", "resolved": "http://registry.m.jd.com/env-paths/download/env-paths-2.2.1.tgz", "integrity": "sha1-QgOZ1BbOH76bwKB8Yvpo1n/Q+PI=", "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/err-code": {"version": "2.0.3", "resolved": "http://registry.m.jd.com/err-code/download/err-code-2.0.3.tgz", "integrity": "sha1-I8Lzt1b/38YI0w4nyalBAkgH5/k=", "license": "MIT", "optional": true}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/es-define-property/download/es-define-property-1.0.1.tgz", "integrity": "sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "http://registry.m.jd.com/es-errors/download/es-errors-1.3.0.tgz", "integrity": "sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "http://registry.m.jd.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz", "integrity": "sha1-HE8sSDcydZfOadLKGQp/3RcjOME=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "resolved": "http://registry.m.jd.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz", "integrity": "sha1-8x274MGDsAptJutjJcgQwP0YvU0=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "http://registry.m.jd.com/escalade/download/escalade-3.2.0.tgz", "integrity": "sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "http://registry.m.jd.com/escape-html/download/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "license": "MIT"}, "node_modules/etag": {"version": "1.8.1", "resolved": "http://registry.m.jd.com/etag/download/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/expand-template": {"version": "2.0.3", "resolved": "http://registry.m.jd.com/expand-template/download/expand-template-2.0.3.tgz", "integrity": "sha1-bhSz/O4POmNA7LV9LokYaSBSpHw=", "license": "(MIT OR WTFPL)", "engines": {"node": ">=6"}}, "node_modules/express": {"version": "5.1.0", "resolved": "http://registry.m.jd.com/express/download/express-5.1.0.tgz", "integrity": "sha1-0xvq9xWgAW8NU/R9O016zyjHXMk=", "license": "MIT", "dependencies": {"accepts": "^2.0.0", "body-parser": "^2.2.0", "content-disposition": "^1.0.0", "content-type": "^1.0.5", "cookie": "^0.7.1", "cookie-signature": "^1.2.1", "debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "finalhandler": "^2.1.0", "fresh": "^2.0.0", "http-errors": "^2.0.0", "merge-descriptors": "^2.0.0", "mime-types": "^3.0.0", "on-finished": "^2.4.1", "once": "^1.4.0", "parseurl": "^1.3.3", "proxy-addr": "^2.0.7", "qs": "^6.14.0", "range-parser": "^1.2.1", "router": "^2.2.0", "send": "^1.1.0", "serve-static": "^2.2.0", "statuses": "^2.0.1", "type-is": "^2.0.1", "vary": "^1.1.2"}, "engines": {"node": ">= 18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/fecha": {"version": "4.2.3", "resolved": "http://registry.m.jd.com/fecha/download/fecha-4.2.3.tgz", "integrity": "sha1-TZzNvGHoYpsln9ymfmWJFEjVaf0=", "license": "MIT"}, "node_modules/file-stream-rotator": {"version": "0.6.1", "resolved": "http://registry.m.jd.com/file-stream-rotator/download/file-stream-rotator-0.6.1.tgz", "integrity": "sha1-AHAZ5zWyYrtsbwGX5Y5ch8uWzsM=", "license": "MIT", "dependencies": {"moment": "^2.29.1"}}, "node_modules/file-uri-to-path": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz", "integrity": "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=", "license": "MIT"}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "http://registry.m.jd.com/fill-range/download/fill-range-7.1.1.tgz", "integrity": "sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "2.1.0", "resolved": "http://registry.m.jd.com/finalhandler/download/finalhandler-2.1.0.tgz", "integrity": "sha1-cjBjc6qJ0FqCQu1WnthqG/98Vh8=", "license": "MIT", "dependencies": {"debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "statuses": "^2.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/fn.name": {"version": "1.1.0", "resolved": "http://registry.m.jd.com/fn.name/download/fn.name-1.1.0.tgz", "integrity": "sha1-JsrYAXlnrqhzG8QpYdBKPVmIrMw=", "license": "MIT"}, "node_modules/follow-redirects": {"version": "1.15.11", "resolved": "http://registry.m.jd.com/follow-redirects/download/follow-redirects-1.15.11.tgz", "integrity": "sha1-d31z1yqS+OxNLkEOtHNSpWuOg0A=", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/form-data": {"version": "4.0.4", "resolved": "http://registry.m.jd.com/form-data/download/form-data-4.0.4.tgz", "integrity": "sha1-eEzczgZpqdaOlNEaxO6pgIjt0sQ=", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/form-data/node_modules/mime-db": {"version": "1.52.0", "resolved": "http://registry.m.jd.com/mime-db/download/mime-db-1.52.0.tgz", "integrity": "sha1-u6vNwChZ9JhzAchW4zh85exDv3A=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/form-data/node_modules/mime-types": {"version": "2.1.35", "resolved": "http://registry.m.jd.com/mime-types/download/mime-types-2.1.35.tgz", "integrity": "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/forwarded": {"version": "0.2.0", "resolved": "http://registry.m.jd.com/forwarded/download/forwarded-0.2.0.tgz", "integrity": "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/fresh/download/fresh-2.0.0.tgz", "integrity": "sha1-jdffahs6Gzpc8YbAWl3SZ2ImNaQ=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/fs-constants": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/fs-constants/download/fs-constants-1.0.0.tgz", "integrity": "sha1-a+Dem+mYzhavivwkSXue6bfM2a0=", "license": "MIT"}, "node_modules/fs-minipass": {"version": "2.1.0", "resolved": "http://registry.m.jd.com/fs-minipass/download/fs-minipass-2.1.0.tgz", "integrity": "sha1-f1A2/b8SxjwWkZDL5BmchSJx+fs=", "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/fs.realpath/download/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "license": "ISC"}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "http://registry.m.jd.com/fsevents/download/fsevents-2.3.3.tgz", "integrity": "sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "http://registry.m.jd.com/function-bind/download/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gauge": {"version": "4.0.4", "resolved": "http://registry.m.jd.com/gauge/download/gauge-4.0.4.tgz", "integrity": "sha1-Uv8GUvK79gepiXk9U7dRvvIyjc4=", "deprecated": "This package is no longer supported.", "license": "ISC", "optional": true, "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "color-support": "^1.1.3", "console-control-strings": "^1.1.0", "has-unicode": "^2.0.1", "signal-exit": "^3.0.7", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "wide-align": "^1.1.5"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/gauge/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "http://registry.m.jd.com/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "license": "MIT", "optional": true, "engines": {"node": ">=8"}}, "node_modules/gauge/node_modules/emoji-regex": {"version": "8.0.0", "resolved": "http://registry.m.jd.com/emoji-regex/download/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "license": "MIT", "optional": true}, "node_modules/gauge/node_modules/string-width": {"version": "4.2.3", "resolved": "http://registry.m.jd.com/string-width/download/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "license": "MIT", "optional": true, "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/gauge/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "http://registry.m.jd.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "license": "MIT", "optional": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "http://registry.m.jd.com/get-caller-file/download/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-east-asian-width": {"version": "1.3.0", "resolved": "http://registry.m.jd.com/get-east-asian-width/download/get-east-asian-width-1.3.0.tgz", "integrity": "sha1-IbQHHuWO0E7g22UzcbVbQpmHU4k=", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "http://registry.m.jd.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz", "integrity": "sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/get-proto/download/get-proto-1.0.1.tgz", "integrity": "sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/github-from-package": {"version": "0.0.0", "resolved": "http://registry.m.jd.com/github-from-package/download/github-from-package-0.0.0.tgz", "integrity": "sha1-l/tdlr/eiXMxPyDoKI75oWf6ZM4=", "license": "MIT"}, "node_modules/glob": {"version": "7.2.3", "resolved": "http://registry.m.jd.com/glob/download/glob-7.2.3.tgz", "integrity": "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=", "deprecated": "Glob versions prior to v9 are no longer supported", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "resolved": "http://registry.m.jd.com/glob-parent/download/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "http://registry.m.jd.com/gopd/download/gopd-1.2.0.tgz", "integrity": "sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "http://registry.m.jd.com/graceful-fs/download/graceful-fs-4.2.11.tgz", "integrity": "sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=", "license": "ISC", "optional": true}, "node_modules/has-flag": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/has-flag/download/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "http://registry.m.jd.com/has-symbols/download/has-symbols-1.1.0.tgz", "integrity": "sha1-/JxqeDoISVHQuXH+EBjegTcHozg=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz", "integrity": "sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-unicode": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/has-unicode/download/has-unicode-2.0.1.tgz", "integrity": "sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=", "license": "ISC"}, "node_modules/hasown": {"version": "2.0.2", "resolved": "http://registry.m.jd.com/hasown/download/hasown-2.0.2.tgz", "integrity": "sha1-AD6vkb563DcuhOxZ3DclLO24AAM=", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/http-cache-semantics": {"version": "4.2.0", "resolved": "http://registry.m.jd.com/http-cache-semantics/download/http-cache-semantics-4.2.0.tgz", "integrity": "sha1-IF9Ntk+FYrdqT/kjWqUnmDmgndU=", "license": "BSD-2-<PERSON><PERSON>", "optional": true}, "node_modules/http-errors": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/http-errors/download/http-errors-2.0.0.tgz", "integrity": "sha1-t3dKFIbvc892Z6ya4IWMASxXudM=", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-errors/node_modules/statuses": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/statuses/download/statuses-2.0.1.tgz", "integrity": "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/http-proxy-agent": {"version": "4.0.1", "resolved": "http://registry.m.jd.com/http-proxy-agent/download/http-proxy-agent-4.0.1.tgz", "integrity": "sha1-ioyO9/WTLM+VPClsqCkblap0qjo=", "license": "MIT", "optional": true, "dependencies": {"@tootallnate/once": "1", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/https-proxy-agent": {"version": "5.0.1", "resolved": "http://registry.m.jd.com/https-proxy-agent/download/https-proxy-agent-5.0.1.tgz", "integrity": "sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY=", "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/humanize-ms": {"version": "1.2.1", "resolved": "http://registry.m.jd.com/humanize-ms/download/humanize-ms-1.2.1.tgz", "integrity": "sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0=", "license": "MIT", "optional": true, "dependencies": {"ms": "^2.0.0"}}, "node_modules/iconv-lite": {"version": "0.6.3", "resolved": "http://registry.m.jd.com/iconv-lite/download/iconv-lite-0.6.3.tgz", "integrity": "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "http://registry.m.jd.com/ieee754/download/ieee754-1.2.1.tgz", "integrity": "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore-by-default": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/ignore-by-default/download/ignore-by-default-1.0.1.tgz", "integrity": "sha1-SMptcvbGo68Aqa1K5odr44ieKwk=", "dev": true, "license": "ISC"}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "http://registry.m.jd.com/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "license": "MIT", "optional": true, "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/indent-string/download/indent-string-4.0.0.tgz", "integrity": "sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=", "license": "MIT", "optional": true, "engines": {"node": ">=8"}}, "node_modules/infer-owner": {"version": "1.0.4", "resolved": "http://registry.m.jd.com/infer-owner/download/infer-owner-1.0.4.tgz", "integrity": "sha1-xM78qo5RBRwqQLos6KPScpWvlGc=", "license": "ISC", "optional": true}, "node_modules/inflection": {"version": "1.13.4", "resolved": "http://registry.m.jd.com/inflection/download/inflection-1.13.4.tgz", "integrity": "sha1-ZappbE4tpiJbFI16FUxEk2ZjOjI=", "engines": ["node >= 0.4.0"], "license": "MIT"}, "node_modules/inflight": {"version": "1.0.6", "resolved": "http://registry.m.jd.com/inflight/download/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "http://registry.m.jd.com/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "license": "ISC"}, "node_modules/ini": {"version": "1.3.8", "resolved": "http://registry.m.jd.com/ini/download/ini-1.3.8.tgz", "integrity": "sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=", "license": "ISC"}, "node_modules/ip-address": {"version": "10.0.1", "resolved": "http://registry.m.jd.com/ip-address/download/ip-address-10.0.1.tgz", "integrity": "sha1-qBgLeDzneId315YobWG85CdoGO0=", "license": "MIT", "optional": true, "engines": {"node": ">= 12"}}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "http://registry.m.jd.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-arrayish": {"version": "0.3.2", "resolved": "http://registry.m.jd.com/is-arrayish/download/is-arrayish-0.3.2.tgz", "integrity": "sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=", "license": "MIT"}, "node_modules/is-binary-path": {"version": "2.1.0", "resolved": "http://registry.m.jd.com/is-binary-path/download/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "dev": true, "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "http://registry.m.jd.com/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "http://registry.m.jd.com/is-glob/download/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-lambda": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/is-lambda/download/is-lambda-1.0.1.tgz", "integrity": "sha1-PZh3iZ5qU+/AFgUEzeFfgubwYdU=", "license": "MIT", "optional": true}, "node_modules/is-number": {"version": "7.0.0", "resolved": "http://registry.m.jd.com/is-number/download/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-promise": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/is-promise/download/is-promise-4.0.0.tgz", "integrity": "sha1-Qv+fhCBsGZHSbev1IN1cAQQt0vM=", "license": "MIT"}, "node_modules/is-stream": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/is-stream/download/is-stream-2.0.1.tgz", "integrity": "sha1-+sHj1TuXrVqdCunO8jifWBClwHc=", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "license": "ISC", "optional": true}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "http://registry.m.jd.com/js-yaml/download/js-yaml-4.1.0.tgz", "integrity": "sha1-wftl+PUBeQHN0slRhkuhhFihBgI=", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/kuler": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/kuler/download/kuler-2.0.0.tgz", "integrity": "sha1-4sVwo4ADiPtEQH6FFTHB1nCwYbM=", "license": "MIT"}, "node_modules/lodash": {"version": "4.17.21", "resolved": "http://registry.m.jd.com/lodash/download/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=", "license": "MIT"}, "node_modules/logform": {"version": "2.7.0", "resolved": "http://registry.m.jd.com/logform/download/logform-2.7.0.tgz", "integrity": "sha1-z8qXUo7ykPLhJaCDloBQArLQYNE=", "license": "MIT", "dependencies": {"@colors/colors": "1.6.0", "@types/triple-beam": "^1.3.2", "fecha": "^4.2.0", "ms": "^2.1.1", "safe-stable-stringify": "^2.3.1", "triple-beam": "^1.3.0"}, "engines": {"node": ">= 12.0.0"}}, "node_modules/lru-cache": {"version": "6.0.0", "resolved": "http://registry.m.jd.com/lru-cache/download/lru-cache-6.0.0.tgz", "integrity": "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=", "license": "ISC", "optional": true, "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/make-dir": {"version": "3.1.0", "resolved": "http://registry.m.jd.com/make-dir/download/make-dir-3.1.0.tgz", "integrity": "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=", "license": "MIT", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-dir/node_modules/semver": {"version": "6.3.1", "resolved": "http://registry.m.jd.com/semver/download/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/make-error": {"version": "1.3.6", "resolved": "http://registry.m.jd.com/make-error/download/make-error-1.3.6.tgz", "integrity": "sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=", "dev": true, "license": "ISC"}, "node_modules/make-fetch-happen": {"version": "9.1.0", "resolved": "http://registry.m.jd.com/make-fetch-happen/download/make-fetch-happen-9.1.0.tgz", "integrity": "sha1-UwhaCeeXFDPmdl95cb9j9OBcuWg=", "license": "ISC", "optional": true, "dependencies": {"agentkeepalive": "^4.1.3", "cacache": "^15.2.0", "http-cache-semantics": "^4.1.0", "http-proxy-agent": "^4.0.1", "https-proxy-agent": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "minipass": "^3.1.3", "minipass-collect": "^1.0.2", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.2", "promise-retry": "^2.0.1", "socks-proxy-agent": "^6.0.0", "ssri": "^8.0.0"}, "engines": {"node": ">= 10"}}, "node_modules/make-fetch-happen/node_modules/negotiator": {"version": "0.6.4", "resolved": "http://registry.m.jd.com/negotiator/download/negotiator-0.6.4.tgz", "integrity": "sha1-d3lI4kUmUcVwtxLdAcI+JicT//c=", "license": "MIT", "optional": true, "engines": {"node": ">= 0.6"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "http://registry.m.jd.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz", "integrity": "sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/media-typer": {"version": "1.1.0", "resolved": "http://registry.m.jd.com/media-typer/download/media-typer-1.1.0.tgz", "integrity": "sha1-ardLjy0zIPIGSyqHo455Mf86VWE=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/merge-descriptors": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/merge-descriptors/download/merge-descriptors-2.0.0.tgz", "integrity": "sha1-6pIvZgY1oiSe5WXgRJ+VHmtgOAg=", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/mime-db": {"version": "1.54.0", "resolved": "http://registry.m.jd.com/mime-db/download/mime-db-1.54.0.tgz", "integrity": "sha1-zds+5PnGRTDf9kAjZmHULLajFPU=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "3.0.1", "resolved": "http://registry.m.jd.com/mime-types/download/mime-types-3.0.1.tgz", "integrity": "sha1-sdlNaZepsy/WnrrtDbc96Ky1Gc4=", "license": "MIT", "dependencies": {"mime-db": "^1.54.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-response": {"version": "3.1.0", "resolved": "http://registry.m.jd.com/mimic-response/download/mimic-response-3.1.0.tgz", "integrity": "sha1-LR1Zr5wbEpgVrMwsRqAipc4fo8k=", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "http://registry.m.jd.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "http://registry.m.jd.com/minimist/download/minimist-1.2.8.tgz", "integrity": "sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "3.3.6", "resolved": "http://registry.m.jd.com/minipass/download/minipass-3.3.6.tgz", "integrity": "sha1-e7o4TbOhUg0YycDlJRw0ROld2Uo=", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-collect": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/minipass-collect/download/minipass-collect-1.0.2.tgz", "integrity": "sha1-IrgTv3Rdxu26JXa5QAIq1u3Ixhc=", "license": "ISC", "optional": true, "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minipass-fetch": {"version": "1.4.1", "resolved": "http://registry.m.jd.com/minipass-fetch/download/minipass-fetch-1.4.1.tgz", "integrity": "sha1-114AkdqsGw/9fp1BYp+v99DB8bY=", "license": "MIT", "optional": true, "dependencies": {"minipass": "^3.1.0", "minipass-sized": "^1.0.3", "minizlib": "^2.0.0"}, "engines": {"node": ">=8"}, "optionalDependencies": {"encoding": "^0.1.12"}}, "node_modules/minipass-flush": {"version": "1.0.5", "resolved": "http://registry.m.jd.com/minipass-flush/download/minipass-flush-1.0.5.tgz", "integrity": "sha1-gucTXX6JpQ/+ZGEKeHlTxMTLs3M=", "license": "ISC", "optional": true, "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minipass-pipeline": {"version": "1.2.4", "resolved": "http://registry.m.jd.com/minipass-pipeline/download/minipass-pipeline-1.2.4.tgz", "integrity": "sha1-aEcveXEcCEZXwGfFxq2Tzd6oIUw=", "license": "ISC", "optional": true, "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-sized": {"version": "1.0.3", "resolved": "http://registry.m.jd.com/minipass-sized/download/minipass-sized-1.0.3.tgz", "integrity": "sha1-cO5afFBSBwr6z7wil36nne81O3A=", "license": "ISC", "optional": true, "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minizlib": {"version": "2.1.2", "resolved": "http://registry.m.jd.com/minizlib/download/minizlib-2.1.2.tgz", "integrity": "sha1-6Q00Zrogm5MkUVCKEc49NjIUWTE=", "license": "MIT", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/mkdirp": {"version": "1.0.4", "resolved": "http://registry.m.jd.com/mkdirp/download/mkdirp-1.0.4.tgz", "integrity": "sha1-PrXtYmInVteaXw4qIh3+utdcL34=", "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/mkdirp-classic": {"version": "0.5.3", "resolved": "http://registry.m.jd.com/mkdirp-classic/download/mkdirp-classic-0.5.3.tgz", "integrity": "sha1-+hDJEVzG2IZb4iG6R+6b7XhgERM=", "license": "MIT"}, "node_modules/moment": {"version": "2.30.1", "resolved": "http://registry.m.jd.com/moment/download/moment-2.30.1.tgz", "integrity": "sha1-+MkcB7enhuMMWZJt9TC06slpdK4=", "license": "MIT", "engines": {"node": "*"}}, "node_modules/moment-timezone": {"version": "0.5.48", "resolved": "http://registry.m.jd.com/moment-timezone/download/moment-timezone-0.5.48.tgz", "integrity": "sha1-ERcnuydHNKUYrhVLXKWJKD8FiWc=", "license": "MIT", "dependencies": {"moment": "^2.29.4"}, "engines": {"node": "*"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "http://registry.m.jd.com/ms/download/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=", "license": "MIT"}, "node_modules/napi-build-utils": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/napi-build-utils/download/napi-build-utils-2.0.0.tgz", "integrity": "sha1-E8IsAYf8/MzhRhhEE2NypH3cAn4=", "license": "MIT"}, "node_modules/negotiator": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/negotiator/download/negotiator-1.0.0.tgz", "integrity": "sha1-tskbtHFy1p+Tz9fDV7u1KQGbX2o=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/node-abi": {"version": "3.75.0", "resolved": "http://registry.m.jd.com/node-abi/download/node-abi-3.75.0.tgz", "integrity": "sha1-L5KakakKDQKzJcQ3MTFIAjV+12Q=", "license": "MIT", "dependencies": {"semver": "^7.3.5"}, "engines": {"node": ">=10"}}, "node_modules/node-addon-api": {"version": "8.5.0", "resolved": "http://registry.m.jd.com/node-addon-api/download/node-addon-api-8.5.0.tgz", "integrity": "sha1-yRstdoL6RX0uHDiBUPDf+ar7jz8=", "license": "MIT", "engines": {"node": "^18 || ^20 || >= 21"}}, "node_modules/node-fetch": {"version": "2.7.0", "resolved": "http://registry.m.jd.com/node-fetch/download/node-fetch-2.7.0.tgz", "integrity": "sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-gyp": {"version": "8.4.1", "resolved": "http://registry.m.jd.com/node-gyp/download/node-gyp-8.4.1.tgz", "integrity": "sha1-PUkwj8MfdoGAlX1rV0aEX71CmTc=", "license": "MIT", "optional": true, "dependencies": {"env-paths": "^2.2.0", "glob": "^7.1.4", "graceful-fs": "^4.2.6", "make-fetch-happen": "^9.1.0", "nopt": "^5.0.0", "npmlog": "^6.0.0", "rimraf": "^3.0.2", "semver": "^7.3.5", "tar": "^6.1.2", "which": "^2.0.2"}, "bin": {"node-gyp": "bin/node-gyp.js"}, "engines": {"node": ">= 10.12.0"}}, "node_modules/node-gyp-build": {"version": "4.8.4", "resolved": "http://registry.m.jd.com/node-gyp-build/download/node-gyp-build-4.8.4.tgz", "integrity": "sha1-inDuhUZK5SMndyqQ1mxgd6kAz8g=", "license": "MIT", "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}, "node_modules/nodejieba": {"version": "3.4.4", "resolved": "http://registry.m.jd.com/nodejieba/download/nodejieba-3.4.4.tgz", "integrity": "sha1-2Ll/hTuNlvqahlnV0DWVjZ6Rn50=", "hasInstallScript": true, "license": "MIT", "dependencies": {"@mapbox/node-pre-gyp": "^1.0.9", "node-addon-api": "^3.0.2"}, "engines": {"node": ">= 10.20.0"}}, "node_modules/nodejieba/node_modules/node-addon-api": {"version": "3.2.1", "resolved": "http://registry.m.jd.com/node-addon-api/download/node-addon-api-3.2.1.tgz", "integrity": "sha1-gTJeCiEXeJwBKNq2Xn448HzroWE=", "license": "MIT"}, "node_modules/nodemon": {"version": "3.1.10", "resolved": "http://registry.m.jd.com/nodemon/download/nodemon-3.1.10.tgz", "integrity": "sha1-UBXF60//yyTZjPlFTfFPT+zsm8E=", "dev": true, "license": "MIT", "dependencies": {"chokidar": "^3.5.2", "debug": "^4", "ignore-by-default": "^1.0.1", "minimatch": "^3.1.2", "pstree.remy": "^1.1.8", "semver": "^7.5.3", "simple-update-notifier": "^2.0.0", "supports-color": "^5.5.0", "touch": "^3.1.0", "undefsafe": "^2.0.5"}, "bin": {"nodemon": "bin/nodemon.js"}, "engines": {"node": ">=10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/nodemon"}}, "node_modules/nopt": {"version": "5.0.0", "resolved": "http://registry.m.jd.com/nopt/download/nopt-5.0.0.tgz", "integrity": "sha1-UwlCu1ilEvzK/lP+IQ8TolNV3Ig=", "license": "ISC", "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": ">=6"}}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/normalize-path/download/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npmlog": {"version": "6.0.2", "resolved": "http://registry.m.jd.com/npmlog/download/npmlog-6.0.2.tgz", "integrity": "sha1-yBZgF6QvLeqS1kUxaN2GUYanCDA=", "deprecated": "This package is no longer supported.", "license": "ISC", "optional": true, "dependencies": {"are-we-there-yet": "^3.0.0", "console-control-strings": "^1.1.0", "gauge": "^4.0.3", "set-blocking": "^2.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "http://registry.m.jd.com/object-assign/download/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-hash": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/object-hash/download/object-hash-3.0.0.tgz", "integrity": "sha1-c/l/dT57r/wOLMnW4HkHl0Ssguk=", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "http://registry.m.jd.com/object-inspect/download/object-inspect-1.13.4.tgz", "integrity": "sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "http://registry.m.jd.com/on-finished/download/on-finished-2.4.1.tgz", "integrity": "sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "http://registry.m.jd.com/once/download/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/one-time": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/one-time/download/one-time-1.0.0.tgz", "integrity": "sha1-4GvBdK7SFO1Y7e3lc7Qzu/gny0U=", "license": "MIT", "dependencies": {"fn.name": "1.x.x"}}, "node_modules/p-map": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/p-map/download/p-map-4.0.0.tgz", "integrity": "sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=", "license": "MIT", "optional": true, "dependencies": {"aggregate-error": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "http://registry.m.jd.com/parseurl/download/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-to-regexp": {"version": "8.2.0", "resolved": "http://registry.m.jd.com/path-to-regexp/download/path-to-regexp-8.2.0.tgz", "integrity": "sha1-c5kMwp5Xo/8qDZFAlRVt9dt56LQ=", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/pg-connection-string": {"version": "2.9.1", "resolved": "http://registry.m.jd.com/pg-connection-string/download/pg-connection-string-2.9.1.tgz", "integrity": "sha1-ux/QAR4ut2rBc2Dcj6GDstNGUjg=", "license": "MIT"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "http://registry.m.jd.com/picomatch/download/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/prebuild-install": {"version": "7.1.3", "resolved": "http://registry.m.jd.com/prebuild-install/download/prebuild-install-7.1.3.tgz", "integrity": "sha1-1jCrrSsUdEPyCiEpF76uaLgJLuw=", "license": "MIT", "dependencies": {"detect-libc": "^2.0.0", "expand-template": "^2.0.3", "github-from-package": "0.0.0", "minimist": "^1.2.3", "mkdirp-classic": "^0.5.3", "napi-build-utils": "^2.0.0", "node-abi": "^3.3.0", "pump": "^3.0.0", "rc": "^1.2.7", "simple-get": "^4.0.0", "tar-fs": "^2.0.0", "tunnel-agent": "^0.6.0"}, "bin": {"prebuild-install": "bin.js"}, "engines": {"node": ">=10"}}, "node_modules/promise-inflight": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/promise-inflight/download/promise-inflight-1.0.1.tgz", "integrity": "sha1-mEcocL8igTL8vdhoEputEsPAKeM=", "license": "ISC", "optional": true}, "node_modules/promise-retry": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/promise-retry/download/promise-retry-2.0.1.tgz", "integrity": "sha1-/3R6E2IKtXumiPX8Z4VUEMNw2iI=", "license": "MIT", "optional": true, "dependencies": {"err-code": "^2.0.2", "retry": "^0.12.0"}, "engines": {"node": ">=10"}}, "node_modules/proxy-addr": {"version": "2.0.7", "resolved": "http://registry.m.jd.com/proxy-addr/download/proxy-addr-2.0.7.tgz", "integrity": "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "http://registry.m.jd.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz", "integrity": "sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=", "license": "MIT"}, "node_modules/pstree.remy": {"version": "1.1.8", "resolved": "http://registry.m.jd.com/pstree.remy/download/pstree.remy-1.1.8.tgz", "integrity": "sha1-wkIiT0pnwh9oaDm720rCgrg3PTo=", "dev": true, "license": "MIT"}, "node_modules/pump": {"version": "3.0.3", "resolved": "http://registry.m.jd.com/pump/download/pump-3.0.3.tgz", "integrity": "sha1-FR2XnxopZo3AAl7FiaRVtTKCJo0=", "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/qs": {"version": "6.14.0", "resolved": "http://registry.m.jd.com/qs/download/qs-6.14.0.tgz", "integrity": "sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "http://registry.m.jd.com/range-parser/download/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/raw-body/download/raw-body-3.0.0.tgz", "integrity": "sha1-JbNHbwelFgBhna4/6C3cKKNuXg8=", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.6.3", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/rc": {"version": "1.2.8", "resolved": "http://registry.m.jd.com/rc/download/rc-1.2.8.tgz", "integrity": "sha1-zZJL9SAKB1uDwYjNa54hG3/A0+0=", "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/readable-stream": {"version": "3.6.2", "resolved": "http://registry.m.jd.com/readable-stream/download/readable-stream-3.6.2.tgz", "integrity": "sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/readdirp": {"version": "3.6.0", "resolved": "http://registry.m.jd.com/readdirp/download/readdirp-3.6.0.tgz", "integrity": "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=", "dev": true, "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/retry": {"version": "0.12.0", "resolved": "http://registry.m.jd.com/retry/download/retry-0.12.0.tgz", "integrity": "sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=", "license": "MIT", "optional": true, "engines": {"node": ">= 4"}}, "node_modules/retry-as-promised": {"version": "7.1.1", "resolved": "http://registry.m.jd.com/retry-as-promised/download/retry-as-promised-7.1.1.tgz", "integrity": "sha1-NiYkbwTBlB/xDOvPo98Fd/2Kstc=", "license": "MIT"}, "node_modules/rimraf": {"version": "3.0.2", "resolved": "http://registry.m.jd.com/rimraf/download/rimraf-3.0.2.tgz", "integrity": "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=", "deprecated": "Rimraf versions prior to v4 are no longer supported", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/router": {"version": "2.2.0", "resolved": "http://registry.m.jd.com/router/download/router-2.2.0.tgz", "integrity": "sha1-AZvmILcRyHZBFnzHm5kJDwCxRu8=", "license": "MIT", "dependencies": {"debug": "^4.4.0", "depd": "^2.0.0", "is-promise": "^4.0.0", "parseurl": "^1.3.3", "path-to-regexp": "^8.0.0"}, "engines": {"node": ">= 18"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "http://registry.m.jd.com/safe-buffer/download/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-stable-stringify": {"version": "2.5.0", "resolved": "http://registry.m.jd.com/safe-stable-stringify/download/safe-stable-stringify-2.5.0.tgz", "integrity": "sha1-TKL444XygxxDKnGbEIo7969Cod0=", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "http://registry.m.jd.com/safer-buffer/download/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "license": "MIT"}, "node_modules/semver": {"version": "7.7.2", "resolved": "http://registry.m.jd.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/send": {"version": "1.2.0", "resolved": "http://registry.m.jd.com/send/download/send-1.2.0.tgz", "integrity": "sha1-MqdVT7d3uDHfqCg3D3c6OAjTchI=", "license": "MIT", "dependencies": {"debug": "^4.3.5", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "fresh": "^2.0.0", "http-errors": "^2.0.0", "mime-types": "^3.0.1", "ms": "^2.1.3", "on-finished": "^2.4.1", "range-parser": "^1.2.1", "statuses": "^2.0.1"}, "engines": {"node": ">= 18"}}, "node_modules/sequelize": {"version": "6.37.7", "resolved": "http://registry.m.jd.com/sequelize/download/sequelize-6.37.7.tgz", "integrity": "sha1-Vab4VVrnbB+9S852sqxfzAoebrY=", "funding": [{"type": "opencollective", "url": "https://opencollective.com/sequelize"}], "license": "MIT", "dependencies": {"@types/debug": "^4.1.8", "@types/validator": "^13.7.17", "debug": "^4.3.4", "dottie": "^2.0.6", "inflection": "^1.13.4", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "pg-connection-string": "^2.6.1", "retry-as-promised": "^7.0.4", "semver": "^7.5.4", "sequelize-pool": "^7.1.0", "toposort-class": "^1.0.1", "uuid": "^8.3.2", "validator": "^13.9.0", "wkx": "^0.5.0"}, "engines": {"node": ">=10.0.0"}, "peerDependenciesMeta": {"ibm_db": {"optional": true}, "mariadb": {"optional": true}, "mysql2": {"optional": true}, "oracledb": {"optional": true}, "pg": {"optional": true}, "pg-hstore": {"optional": true}, "snowflake-sdk": {"optional": true}, "sqlite3": {"optional": true}, "tedious": {"optional": true}}}, "node_modules/sequelize-pool": {"version": "7.1.0", "resolved": "http://registry.m.jd.com/sequelize-pool/download/sequelize-pool-7.1.0.tgz", "integrity": "sha1-IQs5GvQAJ2L4IxiP1uz8dBMCB2g=", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/serve-static": {"version": "2.2.0", "resolved": "http://registry.m.jd.com/serve-static/download/serve-static-2.2.0.tgz", "integrity": "sha1-nAJWTuJZvdIlG4LWWaLn4ZONZvk=", "license": "MIT", "dependencies": {"encodeurl": "^2.0.0", "escape-html": "^1.0.3", "parseurl": "^1.3.3", "send": "^1.2.0"}, "engines": {"node": ">= 18"}}, "node_modules/set-blocking": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/set-blocking/download/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc=", "license": "ISC"}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "http://registry.m.jd.com/setprototypeof/download/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=", "license": "ISC"}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "http://registry.m.jd.com/side-channel/download/side-channel-1.1.0.tgz", "integrity": "sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/side-channel-list/download/side-channel-list-1.0.0.tgz", "integrity": "sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/side-channel-map/download/side-channel-map-1.0.1.tgz", "integrity": "sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz", "integrity": "sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "http://registry.m.jd.com/signal-exit/download/signal-exit-3.0.7.tgz", "integrity": "sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=", "license": "ISC"}, "node_modules/simple-concat": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/simple-concat/download/simple-concat-1.0.1.tgz", "integrity": "sha1-9Gl2CCujXCJj8cirXt/ibEHJVS8=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/simple-get": {"version": "4.0.1", "resolved": "http://registry.m.jd.com/simple-get/download/simple-get-4.0.1.tgz", "integrity": "sha1-SjnbVJKHyXnTUhEvoD/Zn9a8NUM=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"decompress-response": "^6.0.0", "once": "^1.3.1", "simple-concat": "^1.0.0"}}, "node_modules/simple-swizzle": {"version": "0.2.2", "resolved": "http://registry.m.jd.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz", "integrity": "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=", "license": "MIT", "dependencies": {"is-arrayish": "^0.3.1"}}, "node_modules/simple-update-notifier": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/simple-update-notifier/download/simple-update-notifier-2.0.0.tgz", "integrity": "sha1-1wuSvat9bZDf1zkxGVowtuPXzrs=", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}}, "node_modules/smart-buffer": {"version": "4.2.0", "resolved": "http://registry.m.jd.com/smart-buffer/download/smart-buffer-4.2.0.tgz", "integrity": "sha1-bh1x+k8YwF99D/IW3RakgdDo2a4=", "license": "MIT", "optional": true, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/socks": {"version": "2.8.7", "resolved": "http://registry.m.jd.com/socks/download/socks-2.8.7.tgz", "integrity": "sha1-4vsdmmA63XUFCiBn24w4GgtWaeo=", "license": "MIT", "optional": true, "dependencies": {"ip-address": "^10.0.1", "smart-buffer": "^4.2.0"}, "engines": {"node": ">= 10.0.0", "npm": ">= 3.0.0"}}, "node_modules/socks-proxy-agent": {"version": "6.2.1", "resolved": "http://registry.m.jd.com/socks-proxy-agent/download/socks-proxy-agent-6.2.1.tgz", "integrity": "sha1-JoejH51xheONUwvvGUT+HxSW1s4=", "license": "MIT", "optional": true, "dependencies": {"agent-base": "^6.0.2", "debug": "^4.3.3", "socks": "^2.6.2"}, "engines": {"node": ">= 10"}}, "node_modules/sqlite3": {"version": "5.1.7", "resolved": "http://registry.m.jd.com/sqlite3/download/sqlite3-5.1.7.tgz", "integrity": "sha1-WcoQU8GrOGRzllhu2tAZsVUQQbc=", "hasInstallScript": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^7.0.0", "prebuild-install": "^7.1.1", "tar": "^6.1.11"}, "optionalDependencies": {"node-gyp": "8.x"}, "peerDependencies": {"node-gyp": "8.x"}, "peerDependenciesMeta": {"node-gyp": {"optional": true}}}, "node_modules/sqlite3/node_modules/node-addon-api": {"version": "7.1.1", "resolved": "http://registry.m.jd.com/node-addon-api/download/node-addon-api-7.1.1.tgz", "integrity": "sha1-Grpmk7DyVSWKBJ1iEykykyKq1Vg=", "license": "MIT"}, "node_modules/ssri": {"version": "8.0.1", "resolved": "http://registry.m.jd.com/ssri/download/ssri-8.0.1.tgz", "integrity": "sha1-Y45OQ54v+9LNKJd21cpFfE9Roq8=", "license": "ISC", "optional": true, "dependencies": {"minipass": "^3.1.1"}, "engines": {"node": ">= 8"}}, "node_modules/stack-trace": {"version": "0.0.10", "resolved": "http://registry.m.jd.com/stack-trace/download/stack-trace-0.0.10.tgz", "integrity": "sha1-VHxws0fo0ytOEI6hoqFZ5f3eGcA=", "license": "MIT", "engines": {"node": "*"}}, "node_modules/statuses": {"version": "2.0.2", "resolved": "http://registry.m.jd.com/statuses/download/statuses-2.0.2.tgz", "integrity": "sha1-j3XuzvdlteHPzcCA2llAntQk44I=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/string_decoder": {"version": "1.3.0", "resolved": "http://registry.m.jd.com/string_decoder/download/string_decoder-1.3.0.tgz", "integrity": "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-width": {"version": "7.2.0", "resolved": "http://registry.m.jd.com/string-width/download/string-width-7.2.0.tgz", "integrity": "sha1-tbuOIWXOJ11NQ0dt0nAK2Qkdttw=", "license": "MIT", "dependencies": {"emoji-regex": "^10.3.0", "get-east-asian-width": "^1.0.0", "strip-ansi": "^7.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/strip-ansi": {"version": "7.1.0", "resolved": "http://registry.m.jd.com/strip-ansi/download/strip-ansi-7.1.0.tgz", "integrity": "sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/strip-json-comments": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz", "integrity": "sha1-PFMZQukIwml8DsNEhYwobHygpgo=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/supports-color": {"version": "5.5.0", "resolved": "http://registry.m.jd.com/supports-color/download/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/tar": {"version": "6.2.1", "resolved": "http://registry.m.jd.com/tar/download/tar-6.2.1.tgz", "integrity": "sha1-cXVJxUG8PCrxV1G+qUsd0GjUsDo=", "license": "ISC", "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/tar-fs": {"version": "2.1.3", "resolved": "http://registry.m.jd.com/tar-fs/download/tar-fs-2.1.3.tgz", "integrity": "sha1-+zuIQ6JrbxOgjmBveSKHXrH7v5I=", "license": "MIT", "dependencies": {"chownr": "^1.1.1", "mkdirp-classic": "^0.5.2", "pump": "^3.0.0", "tar-stream": "^2.1.4"}}, "node_modules/tar-fs/node_modules/chownr": {"version": "1.1.4", "resolved": "http://registry.m.jd.com/chownr/download/chownr-1.1.4.tgz", "integrity": "sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=", "license": "ISC"}, "node_modules/tar-stream": {"version": "2.2.0", "resolved": "http://registry.m.jd.com/tar-stream/download/tar-stream-2.2.0.tgz", "integrity": "sha1-rK2EwoQTawYNw/qmRHSqmuvXcoc=", "license": "MIT", "dependencies": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "engines": {"node": ">=6"}}, "node_modules/tar/node_modules/minipass": {"version": "5.0.0", "resolved": "http://registry.m.jd.com/minipass/download/minipass-5.0.0.tgz", "integrity": "sha1-PpeI/7kLaUpdDslEeaRbXYc4Ez0=", "license": "ISC", "engines": {"node": ">=8"}}, "node_modules/text-hex": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/text-hex/download/text-hex-1.0.0.tgz", "integrity": "sha1-adycGxdEbueakr9biEu0uRJ1BvU=", "license": "MIT"}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "http://registry.m.jd.com/to-regex-range/download/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/toidentifier/download/toidentifier-1.0.1.tgz", "integrity": "sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/toposort-class": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/toposort-class/download/toposort-class-1.0.1.tgz", "integrity": "sha1-f/0feMi+KMO6Rc1OGj9e4ZO9mYg=", "license": "MIT"}, "node_modules/touch": {"version": "3.1.1", "resolved": "http://registry.m.jd.com/touch/download/touch-3.1.1.tgz", "integrity": "sha1-CXoj17FhR2Q15cE0SpXA91tKVpQ=", "dev": true, "license": "ISC", "bin": {"nodetouch": "bin/nodetouch.js"}}, "node_modules/tr46": {"version": "0.0.3", "resolved": "http://registry.m.jd.com/tr46/download/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=", "license": "MIT"}, "node_modules/tree-sitter": {"version": "0.21.1", "resolved": "http://registry.m.jd.com/tree-sitter/download/tree-sitter-0.21.1.tgz", "integrity": "sha1-+7NMCQVnAIFK8OHjdojgZGO6BMQ=", "hasInstallScript": true, "license": "MIT", "dependencies": {"node-addon-api": "^8.0.0", "node-gyp-build": "^4.8.0"}}, "node_modules/tree-sitter-javascript": {"version": "0.23.1", "resolved": "http://registry.m.jd.com/tree-sitter-javascript/download/tree-sitter-javascript-0.23.1.tgz", "integrity": "sha1-Tkn6OtX5lrOsXPT3jWxd2HZ4j68=", "hasInstallScript": true, "license": "MIT", "dependencies": {"node-addon-api": "^8.2.2", "node-gyp-build": "^4.8.2"}, "peerDependencies": {"tree-sitter": "^0.21.1"}, "peerDependenciesMeta": {"tree-sitter": {"optional": true}}}, "node_modules/triple-beam": {"version": "1.4.1", "resolved": "http://registry.m.jd.com/triple-beam/download/triple-beam-1.4.1.tgz", "integrity": "sha1-b95wJx3G5dc8oMOyTi2Sr7dEGYQ=", "license": "MIT", "engines": {"node": ">= 14.0.0"}}, "node_modules/ts-node": {"version": "10.9.2", "resolved": "http://registry.m.jd.com/ts-node/download/ts-node-10.9.2.tgz", "integrity": "sha1-cPAhyeGFvM3Kgg4m3EE4BcEBxx8=", "dev": true, "license": "MIT", "dependencies": {"@cspotcode/source-map-support": "^0.8.0", "@tsconfig/node10": "^1.0.7", "@tsconfig/node12": "^1.0.7", "@tsconfig/node14": "^1.0.0", "@tsconfig/node16": "^1.0.2", "acorn": "^8.4.1", "acorn-walk": "^8.1.1", "arg": "^4.1.0", "create-require": "^1.1.0", "diff": "^4.0.1", "make-error": "^1.1.1", "v8-compile-cache-lib": "^3.0.1", "yn": "3.1.1"}, "bin": {"ts-node": "dist/bin.js", "ts-node-cwd": "dist/bin-cwd.js", "ts-node-esm": "dist/bin-esm.js", "ts-node-script": "dist/bin-script.js", "ts-node-transpile-only": "dist/bin-transpile.js", "ts-script": "dist/bin-script-deprecated.js"}, "peerDependencies": {"@swc/core": ">=1.2.50", "@swc/wasm": ">=1.2.50", "@types/node": "*", "typescript": ">=2.7"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "@swc/wasm": {"optional": true}}}, "node_modules/tunnel-agent": {"version": "0.6.0", "resolved": "http://registry.m.jd.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/type-is": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/type-is/download/type-is-2.0.1.tgz", "integrity": "sha1-ZPbPA/kvzkAVwrIkeT9r3UsGjJc=", "license": "MIT", "dependencies": {"content-type": "^1.0.5", "media-typer": "^1.1.0", "mime-types": "^3.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/typescript": {"version": "5.9.2", "resolved": "http://registry.m.jd.com/typescript/download/typescript-5.9.2.tgz", "integrity": "sha1-2TRQzd7FFUotXKvjuBArgzFvsqY=", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/undefsafe": {"version": "2.0.5", "resolved": "http://registry.m.jd.com/undefsafe/download/undefsafe-2.0.5.tgz", "integrity": "sha1-OHM7kye9zSJtuIn7cjpu/RYubiw=", "dev": true, "license": "MIT"}, "node_modules/undici-types": {"version": "6.21.0", "resolved": "http://registry.m.jd.com/undici-types/download/undici-types-6.21.0.tgz", "integrity": "sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=", "license": "MIT"}, "node_modules/unique-filename": {"version": "1.1.1", "resolved": "http://registry.m.jd.com/unique-filename/download/unique-filename-1.1.1.tgz", "integrity": "sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=", "license": "ISC", "optional": true, "dependencies": {"unique-slug": "^2.0.0"}}, "node_modules/unique-slug": {"version": "2.0.2", "resolved": "http://registry.m.jd.com/unique-slug/download/unique-slug-2.0.2.tgz", "integrity": "sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=", "license": "ISC", "optional": true, "dependencies": {"imurmurhash": "^0.1.4"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/unpipe/download/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/util-deprecate/download/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "license": "MIT"}, "node_modules/uuid": {"version": "8.3.2", "resolved": "http://registry.m.jd.com/uuid/download/uuid-8.3.2.tgz", "integrity": "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I=", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/v8-compile-cache-lib": {"version": "3.0.1", "resolved": "http://registry.m.jd.com/v8-compile-cache-lib/download/v8-compile-cache-lib-3.0.1.tgz", "integrity": "sha1-Yzbo1xllyz01obu3hoRFp8BSZL8=", "dev": true, "license": "MIT"}, "node_modules/validator": {"version": "13.15.15", "resolved": "http://registry.m.jd.com/validator/download/validator-13.15.15.tgz", "integrity": "sha1-JGWUvlZx3Anao1yuxWifzRjG5+Q=", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "http://registry.m.jd.com/vary/download/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "http://registry.m.jd.com/webidl-conversions/download/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/whatwg-url": {"version": "5.0.0", "resolved": "http://registry.m.jd.com/whatwg-url/download/whatwg-url-5.0.0.tgz", "integrity": "sha1-lmRU6HZUYuN2RNNib2dCzotwll0=", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/which": {"version": "2.0.2", "resolved": "http://registry.m.jd.com/which/download/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "license": "ISC", "optional": true, "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/wide-align": {"version": "1.1.5", "resolved": "http://registry.m.jd.com/wide-align/download/wide-align-1.1.5.tgz", "integrity": "sha1-3x1MIGhUNp7PPJpImPGyP72dFdM=", "license": "ISC", "dependencies": {"string-width": "^1.0.2 || 2 || 3 || 4"}}, "node_modules/wide-align/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "http://registry.m.jd.com/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/wide-align/node_modules/emoji-regex": {"version": "8.0.0", "resolved": "http://registry.m.jd.com/emoji-regex/download/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "license": "MIT"}, "node_modules/wide-align/node_modules/string-width": {"version": "4.2.3", "resolved": "http://registry.m.jd.com/string-width/download/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/wide-align/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "http://registry.m.jd.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/winston": {"version": "3.17.0", "resolved": "http://registry.m.jd.com/winston/download/winston-3.17.0.tgz", "integrity": "sha1-dLhmXOm06nsp0JIs/M+FKgihFCM=", "license": "MIT", "dependencies": {"@colors/colors": "^1.6.0", "@dabh/diagnostics": "^2.0.2", "async": "^3.2.3", "is-stream": "^2.0.0", "logform": "^2.7.0", "one-time": "^1.0.0", "readable-stream": "^3.4.0", "safe-stable-stringify": "^2.3.1", "stack-trace": "0.0.x", "triple-beam": "^1.3.0", "winston-transport": "^4.9.0"}, "engines": {"node": ">= 12.0.0"}}, "node_modules/winston-daily-rotate-file": {"version": "5.0.0", "resolved": "http://registry.m.jd.com/winston-daily-rotate-file/download/winston-daily-rotate-file-5.0.0.tgz", "integrity": "sha1-jNlIAAJUkOR8AOyJK2VaWCH0Jm0=", "license": "MIT", "dependencies": {"file-stream-rotator": "^0.6.1", "object-hash": "^3.0.0", "triple-beam": "^1.4.1", "winston-transport": "^4.7.0"}, "engines": {"node": ">=8"}, "peerDependencies": {"winston": "^3"}}, "node_modules/winston-transport": {"version": "4.9.0", "resolved": "http://registry.m.jd.com/winston-transport/download/winston-transport-4.9.0.tgz", "integrity": "sha1-O7o0XeECl2VOpvM1GUJFYAA7O/k=", "license": "MIT", "dependencies": {"logform": "^2.7.0", "readable-stream": "^3.6.2", "triple-beam": "^1.3.0"}, "engines": {"node": ">= 12.0.0"}}, "node_modules/wkx": {"version": "0.5.0", "resolved": "http://registry.m.jd.com/wkx/download/wkx-0.5.0.tgz", "integrity": "sha1-xsNwGaz0DlF8xrlGV6JaPUqjPow=", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/wrap-ansi": {"version": "9.0.0", "resolved": "http://registry.m.jd.com/wrap-ansi/download/wrap-ansi-9.0.0.tgz", "integrity": "sha1-Gj3Itw2F7rg5jd+x5KAs0Ybliz4=", "license": "MIT", "dependencies": {"ansi-styles": "^6.2.1", "string-width": "^7.0.0", "strip-ansi": "^7.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/wrappy/download/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "license": "ISC"}, "node_modules/y18n": {"version": "5.0.8", "resolved": "http://registry.m.jd.com/y18n/download/y18n-5.0.8.tgz", "integrity": "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/yallist/download/yallist-4.0.0.tgz", "integrity": "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=", "license": "ISC"}, "node_modules/yargs": {"version": "18.0.0", "resolved": "http://registry.m.jd.com/yargs/download/yargs-18.0.0.tgz", "integrity": "sha1-bIQlmAYnOnRrCfV5CHtoo8LSW9E=", "license": "MIT", "dependencies": {"cliui": "^9.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "string-width": "^7.2.0", "y18n": "^5.0.5", "yargs-parser": "^22.0.0"}, "engines": {"node": "^20.19.0 || ^22.12.0 || >=23"}}, "node_modules/yargs-parser": {"version": "22.0.0", "resolved": "http://registry.m.jd.com/yargs-parser/download/yargs-parser-22.0.0.tgz", "integrity": "sha1-h7gglAUbBWdxc0bs0A/RSASzV8g=", "license": "ISC", "engines": {"node": "^20.19.0 || ^22.12.0 || >=23"}}, "node_modules/yn": {"version": "3.1.1", "resolved": "http://registry.m.jd.com/yn/download/yn-3.1.1.tgz", "integrity": "sha1-HodAGgnXZ8HV6rJqbkwYUYLS61A=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}}}