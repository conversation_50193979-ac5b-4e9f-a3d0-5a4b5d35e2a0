{"name": "node-backend", "version": "1.0.0", "description": "", "main": "main.ts", "scripts": {"dev": "nodemon --exec ts-node main.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/express": "^5.0.3", "@types/js-yaml": "^4.0.9", "@types/minimatch": "^5.1.2", "@types/node": "^20.12.12", "@types/xmldom": "^0.1.34", "@types/yargs": "^17.0.33", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.4.5"}, "dependencies": {"@xmldom/xmldom": "^0.8.11", "axios": "^1.11.0", "express": "^5.1.0", "js-yaml": "^4.1.0", "nodejieba": "^3.4.4", "sequelize": "^6.37.7", "sqlite3": "^5.1.7", "tree-sitter": "^0.21.1", "tree-sitter-javascript": "^0.23.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "yargs": "^18.0.0"}}