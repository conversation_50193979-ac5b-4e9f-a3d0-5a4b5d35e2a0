# Node.js Backend - DeepSearch 代码搜索引擎

一个基于TypeScript的高性能代码搜索引擎后端服务，提供多种搜索算法和智能代码分析功能。

## 🚀 功能特性

### 核心搜索功能
- **倒排索引搜索** - 快速多关键词匹配
- **BM25稀疏搜索** - 基于TF-IDF的相关性搜索
- **正则表达式搜索** - 精确模式匹配（基于ripgrep）
- **智能术语提取** - 支持驼峰命名和下划线命名解析

### 多语言支持
- **中文分词** - 集成nodejieba进行中文文本处理
- **代码分析** - 智能识别函数名、类名、变量名等
- **文档处理** - 支持代码注释和文档内容搜索

### 技术特性
- **TypeScript** - 完整类型安全
- **模块化设计** - 可扩展的搜索工具架构
- **数据库集成** - SQLite数据存储
- **RESTful API** - 标准HTTP接口

## 📦 安装与配置

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0
- TypeScript >= 4.0.0

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd backend/node
```

2. **安装依赖**
```bash
npm install
```

3. **配置数据库**
```bash
# 数据库文件会自动创建在 db.sqlite
# 首次运行时会自动初始化表结构
```

4. **启动服务**
```bash
npm start
```

服务将在 `http://localhost:3451` 启动

## 🔧 使用方法

### 基本API调用

#### 1. 健康检查
```bash
curl http://localhost:3451/health
```

#### 2. 代码搜索
```bash
# 基本搜索
curl -X POST http://localhost:3451/api/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "function definition",
    "repoId": "your-repo-id",
    "searchTool": "INVERTED_INDEX"
  }'
```

#### 3. 搜索工具类型
- `INVERTED_INDEX` - 倒排索引搜索（推荐用于多关键词）
- `TERM_SPRSE` - BM25稀疏搜索（推荐用于相关性搜索）
- `GREP` - 正则表达式搜索（推荐用于精确匹配）

### DeepSearch 模块使用

#### 基本使用示例

```typescript
import { DeepSearch } from './modules/deepsearch/deep_search';
import { SearchToolEnum } from './modules/common/constants';

// 创建DeepSearch实例
const deepSearch = new DeepSearch(
    'repo-id',
    '/path/to/repository',
    'Repository Description',
    null, // LLM客户端（可选）
    SearchToolEnum.INVERTED_INDEX
);

// 初始化
await deepSearch.init();

// 执行搜索
const result = await deepSearch.searchAsync('search query');
console.log(result);
```

#### 搜索工具工厂

```typescript
import { getSearchTool } from './modules/deepsearch/search_factory';
import { SearchToolEnum } from './modules/common/constants';

// 创建特定搜索工具
const searchTool = getSearchTool('repo-id', SearchToolEnum.INVERTED_INDEX);
await searchTool.init();

const results = await searchTool.search('query');
```

#### 术语提取

```typescript
import { getTerms, split_code_terms } from './modules/term/term';

// 提取代码术语
const code = 'function calculateSum(numbers) { return sum; }';
const terms = getTerms(code);
console.log(terms); // ['function', 'calculatesum', 'calculate', 'sum', ...]

// 分割代码术语（返回Set）
const termSet = split_code_terms(code);
console.log(Array.from(termSet));
```

## 📁 项目结构

```
backend/node/
├── modules/
│   ├── deepsearch/           # 核心搜索模块
│   │   ├── deep_search.ts    # 主搜索引擎
│   │   ├── inverted_index_search.ts  # 倒排索引搜索
│   │   ├── term_sparse_search.ts     # BM25稀疏搜索
│   │   ├── grep_search.ts    # 正则表达式搜索
│   │   └── search_factory.ts # 搜索工具工厂
│   ├── term/                 # 术语处理模块
│   │   └── term.ts          # 文本分词和术语提取
│   ├── chunks/              # 代码块处理
│   ├── integrations/        # 外部集成
│   │   └── database/        # 数据库集成
│   └── common/              # 公共常量和工具
├── utils/                   # 工具函数
│   ├── counter.ts          # 计数器工具
│   ├── file.ts             # 文件处理
│   └── trace_logger.ts     # 日志工具
├── server/                 # HTTP服务器
├── config/                 # 配置文件
└── main.ts                # 应用入口
```

## 🔍 搜索算法详解

### 1. 倒排索引搜索 (InvertedIndexSearch)
- **适用场景**: 多关键词快速匹配
- **优势**: 查询速度快，支持布尔查询
- **原理**: 预建立术语到文档的映射索引

### 2. BM25稀疏搜索 (TermSparseSearch)
- **适用场景**: 相关性排序，模糊功能发现
- **优势**: 考虑词频和文档频率，结果更相关
- **原理**: 基于TF-IDF和BM25算法计算相关性得分

### 3. 正则表达式搜索 (GrepSearch)
- **适用场景**: 精确模式匹配，复杂查询
- **优势**: 支持正则表达式，查询灵活
- **原理**: 基于ripgrep进行高性能文本搜索

## 🛠️ 开发指南

### 添加新的搜索工具

1. **实现ISearchTool接口**
```typescript
import { ISearchTool, SearchResult } from './search_factory';

export class CustomSearchTool implements ISearchTool {
    async init(): Promise<void> {
        // 初始化逻辑
    }
    
    async search(query: string): Promise<SearchResult[]> {
        // 搜索实现
        return [];
    }
}
```

2. **注册到工厂类**
```typescript
// 在search_factory.ts中添加
import { CustomSearchTool } from './custom_search';

export function getSearchTool(repoId: string, toolType: SearchToolEnum): ISearchTool {
    switch (toolType) {
        case SearchToolEnum.CUSTOM:
            return new CustomSearchTool(repoId);
        // ...
    }
}
```

### 扩展术语提取

```typescript
// 在term.ts中添加自定义分词逻辑
export function customTermExtractor(text: string): string[] {
    // 自定义提取逻辑
    return [];
}
```

## 📊 性能优化

### 搜索性能
- **索引预建**: 使用倒排索引提高查询速度
- **缓存机制**: 缓存常用查询结果
- **分页查询**: 支持结果分页，避免大量数据传输

### 内存优化
- **流式处理**: 大文件采用流式读取
- **术语去重**: 使用Set数据结构避免重复术语
- **垃圾回收**: 及时释放不需要的对象引用

## 🔧 配置选项

### 环境变量
```bash
# 服务端口
PORT=3451

# 数据库路径
DB_PATH=./db.sqlite

# 日志级别
LOG_LEVEL=info

# 搜索结果限制
MAX_SEARCH_RESULTS=100
```

### 配置文件
```typescript
// config/search.ts
export const searchConfig = {
    maxResults: 100,
    timeout: 30000,
    enableCache: true,
    cacheSize: 1000
};
```

## 🧪 测试

### 运行测试
```bash
# TypeScript编译检查
npx tsc --noEmit

# 严格模式检查
npx tsc --noEmit --strict
```

### 功能测试
```typescript
// 创建测试文件
import { testBasicFunctionality } from './test_functionality';

// 运行功能测试
await testBasicFunctionality();
```

## 📝 API文档

### 搜索接口

**POST** `/api/search`

**请求体**:
```json
{
  "query": "搜索关键词",
  "repoId": "仓库ID",
  "searchTool": "INVERTED_INDEX|TERM_SPRSE|GREP",
  "maxResults": 50,
  "offset": 0
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "filePath": "文件路径",
        "score": 0.95,
        "chunks": [
          {
            "content": "代码内容",
            "startLine": 10,
            "endLine": 20
          }
        ]
      }
    ],
    "total": 100,
    "query": "原始查询",
    "searchTool": "使用的搜索工具"
  }
}
```

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 确认Node.js版本兼容性
   - 查看错误日志

2. **搜索结果为空**
   - 确认仓库路径正确
   - 检查数据库是否初始化
   - 验证搜索工具配置

3. **中文分词异常**
   - 确认nodejieba安装正确
   - 检查文本编码格式

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 创建 [Issue](../../issues)
- 发送邮件到项目维护者
- 查看 [Wiki](../../wiki) 获取更多文档

---

**快速开始**: `npm install && npm start`

**在线文档**: http://localhost:3451/docs (启动服务后访问)